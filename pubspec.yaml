name: flower_timemachine
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is  used as the build suffix.
version: 2.14.0+2

environment:
  sdk: '^3.0.0'
#  flutter: '3.10.5'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.10+1
  sqflite: ^2.3.3+1
  path_provider: ^2.1.4
  #  flutter_picker: 2.1.0
  flutter_picker_plus: ^1.3.0
  image_picker: ^1.1.2
  pull_down_button: ^0.10.1
  extended_image: ^8.2.1
  image_editor: ^1.5.1
  fluttertoast: ^8.2.8
  intl: ^0.19.0
  photo_view: ^0.15.0
  flutter_local_notifications: ^17.2.2
  shared_preferences: ^2.3.2
  image_gallery_saver: ^2.0.3
  mime: ^1.0.5
  in_app_review: ^2.0.9
  encrypt: ^5.0.3
  share_plus: ^10.0.2
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5
  hooks_riverpod: ^2.5.2
  freezed_annotation: ^2.4.4
  flutter_hooks: ^0.20.5
  posthog_flutter: ^4.5.0
  dotted_border: ^2.1.0
  photo_manager: ^3.2.3
  wechat_assets_picker: ^9.5.0
  video_player: ^2.9.5
  app_settings: ^5.1.1
  sentry_flutter: ^8.7.0
  screenshot: ^3.0.0
  webview_flutter: ^4.8.0
  dio: ^5.6.0
  package_info_plus: ^8.0.2
  json_annotation: ^4.9.0
  url_launcher: ^6.3.0
  in_app_purchase: ^3.2.0
  modal_bottom_sheet: ^3.0.0
  flutter_secure_storage: ^9.2.2
  uuid: ^4.4.2
  flutter_easyloading: 3.0.5
  flutter_image_compress: ^2.3.0
  icloud_storage: 2.2.0
  easy_localization: ^3.0.7
  sentry_dart_plugin: ^2.1.0
  icloud_availability:
    path: ../icloud_availability
  flutter_quill: ^10.8.5
  table_calendar: ^3.1.3
  showcaseview: ^4.0.1
  popover: ^0.3.1
  lpinyin: ^2.0.3
  r_string_transform:
    path: ../r_string_transform/r_string_transform
  font_awesome_flutter: ^10.8.0
  get_thumbnail_video: ^0.7.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  custom_lint: ^0.6.5
  riverpod_lint: ^2.3.13
  build_runner: ^2.4.11
  riverpod_generator: ^2.4.3
  freezed: ^2.5.7
  json_serializable: ^6.8.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - icons/filter.svg
    - icons/add.svg
    - icons/water.svg
    - icons/fertilizer.svg
    - icons/camera.svg
    - icons/edit.svg
    - icons/pest_control.svg
    - icons/flower.png
    - icons/heart.svg
    - icons/qrcode.png
    - icons/horn.svg
    - icons/tips.svg
    - icons/vip.svg
    - icons/cut.svg
    - icons/water1.svg
    - icons/nurture.svg
    - icons/spray.svg
    - icons/flower1.svg
    - icons/shovel.svg
    - assets/remind_help.html
    - assets/translations/
    - icons/flower.jpg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

sentry:
  upload_debug_symbols: true
  upload_source_maps: true
  upload_sources: false
  project: flower
  org: chenwu
  auth_token: 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
  wait_for_processing: false
  log_level: error
#  release: ...
#  dist: ...
#  web_build_path: ...
  commits: auto
  ignore_missing: true
#  http_proxy: http://127.0.0.1:7890
