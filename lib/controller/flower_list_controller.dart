import 'dart:async';

import 'package:flower_timemachine/controller/tag_list_controller.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/models/tag_info_mapping.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:sqflite/sqflite.dart';

import '../common/global.dart';
import '../models/flower.dart';
import '../models/maintenance_record.dart';
import '../models/next_task_time.dart';
import '../models/tag_info.dart';
import '../types/flower_list_controller_event.dart';
import '../types/flower_list_sort_type.dart';

part 'flower_list_controller.g.dart';

final ValueNotifier<FlowerListEventType?> flowerListControllerEventProvider = ValueNotifier(null);

final currentFlowerListSortTypeProvider =
    StateProvider.autoDispose<FlowerListSortType>((ref) => ShareConfig.getFlowerListSortType());

@riverpod
FlowerListController flowerListController(FlowerListControllerRef ref) {
  final sortType = ref.watch(currentFlowerListSortTypeProvider);
  final tag = ref.watch(currentTagProvider);

  return FlowerListController(limit: 100, sortType: sortType, currentTag: tag);
}

@riverpod
FutureOr<List<Flower>> flowerListLoader(FlowerListLoaderRef ref) async {
  final controller = ref.watch(flowerListControllerProvider);
  return await controller.load();
}

class FlowerListController {
  final int limit;
  final List<Flower> _list = [];

  // 排序方式
  final FlowerListSortType _sortType;

  // 查询游标
  QueryCursor? _cursor;

  // 是否有更多
  bool _hasMore = true;

  // 正在加载
  bool _loading = false;

  // 标签
  final TagInfo currentTag;

  // 加载 flower 的时间
  DateTime? _flowerLoadTime;

  FlowerListController({required this.limit, required FlowerListSortType sortType, required this.currentTag})
      : _sortType = sortType {
    // 清零时分秒
    final now = DateTime.now();
    _flowerLoadTime = DateTime(now.year, now.month, now.day);
  }

  Flower? get(int index) => _list.elementAtOrNull(index);

  Future<List<Flower>> load() async {
    if (_loading) {
      return List<Flower>.empty();
    }
    _loading = true;

    final cursor = _cursor ??= await _createCursor();

    int i = 0;
    List<Flower> currentList = [];
    while (await cursor.moveNext()) {
      i += 1;
      currentList.add(await Flower.createFromDBRow(cursor.current));
      if (i >= limit) {
        break;
      }
    }

    if (i == 0) {
      _loading = false;
      return List<Flower>.empty();
    }

    _list.addAll(currentList);

    _hasMore = i == limit;
    _loading = false;
    return currentList;
  }

  int addFlower(Flower flower) {
    final insertIndex = _getIndex(flower);
    _list.insert(insertIndex, flower);
    return insertIndex;
  }

  void clear() {
    _cursor?.close();
    _cursor = null;

    for (final f in _list) {
      f.dispose();
    }
    _list.clear();
    _hasMore = true;

    return;
  }

  (int, int)? sortFlower(Flower flower) {
    final currentIndex = _list.indexOf(flower);
    if (currentIndex < 0) {
      debugPrint('不可能');
      return null;
    }

    int newIndex = _getIndex(flower);
    if (newIndex > currentIndex) {
      // 因为 flower 还在 _list 中，在它之后的 index 应该 -1 才是应该插入的位置
      newIndex -= 1;
    }
    if (newIndex == currentIndex) {
      // 位置没有发生变化
      return null;
    }

    return (currentIndex, newIndex);
  }

  int findFlower(Flower flower) {
    return _list.indexOf(flower);
  }

  void deleteByIndex(int index) {
    _list.removeAt(index);
  }

  void insertByIndex(int index, Flower flower) {
    _list.insert(index, flower);
  }

  Future<void> fireAutoNurture(Iterable<Flower> flowers) async {
    final now = DateTime.now();
    final nowTimeStamp = (now.millisecondsSinceEpoch / 1000).truncate();

    for (final flower in flowers) {
      if (flower.nextTaskRecord == null || flower.nextTaskRecord!.day > 0) {
        continue;
      }

      await MaintenanceRecord.batchInsertByTypes(flower.id, flower.nextTaskRecord!.types, nowTimeStamp, null);
      await flower.recalcTaskRecord();
    }
  }

  Future<void> fireBulkNurture(NurtureType type, Iterable<Flower> flowers, DateTime? date, String? remark) async {
    date = date ?? DateTime.now();

    for (final flower in flowers) {
      await MaintenanceRecord.insertByType(flower.id, type, date, remark);
      await flower.recalcTaskRecord();
    }
  }

  Future<void> refreshFlowerDateIfDateUpdate() async {
    // 清零时分秒
    final now = DateTime.now();
    final nowDay = DateTime(now.year, now.month, now.day);
    if (_flowerLoadTime == nowDay) {
      return;
    }

    for (final flower in _list) {
      await flower.recalcTaskRecord(notify: true);
    }

    _flowerLoadTime = nowDay;
  }

  int _getIndex(Flower flower) {
    switch (_sortType) {
      case FlowerListSortType.nextMaintenanceDesc:
        return _getIndexByNextMaintenanceDesc(flower);
      case FlowerListSortType.nextMaintenanceAsc:
        return _getIndexByNextMaintenanceAsc(flower);
      case FlowerListSortType.companionTimeAsc:
        for (int i = 0; i < _list.length; i++) {
          if (_list[i].dayOfCreate > flower.dayOfCreate) {
            return i;
          }
        }
        return _list.length;
      case FlowerListSortType.companionTimeDesc:
        for (int i = 0; i < _list.length; i++) {
          if (_list[i].dayOfCreate < flower.dayOfCreate) {
            return i;
          }
        }
        return _list.length;
      case FlowerListSortType.nameAsc:
        for (int i = 0; i < _list.length; i++) {
          if ((_list[i].normalizedName?.compareTo(flower.normalizedName ?? '') ?? 0) < 0) {
            return i;
          }
        }
        return _list.length;
      case FlowerListSortType.nameDesc:
        for (int i = 0; i < _list.length; i++) {
          if ((_list[i].normalizedName?.compareTo(flower.normalizedName ?? '') ?? 0) > 0) {
            return i;
          }
        }
        return _list.length;
    }
  }

  int _getIndexByNextMaintenanceAsc(Flower flower) {
    if (flower.nextTaskRecord == null) {
      return _list.length;
    }

    for (int i = 0; i < _list.length; i++) {
      final item = _list[i];
      final itemNextTaskRecord = item.nextTaskRecord;

      // 1. 插入的 flower 有养护周期，所以插在没有养护周期的签名
      // 2. 被插入的 flower 养护周期 > 插入的 flower 养护周期
      if (itemNextTaskRecord == null || itemNextTaskRecord.day > flower.nextTaskRecord!.day) {
        return i;
      }
    }

    return _list.length;
  }

  int _getIndexByNextMaintenanceDesc(Flower flower) {
    if (flower.nextTaskRecord == null) {
      return _list.length;
    }

    for (int i = 0; i < _list.length; i++) {
      final item = _list[i];
      final itemNextTaskRecord = item.nextTaskRecord;

      // 1. 插入的 flower 有养护周期，所以插在没有养护周期的前面
      // 2. 被插入的 flower 养护周期 > 插入的 flower 养护周期
      if (itemNextTaskRecord == null || itemNextTaskRecord.day > flower.nextTaskRecord!.day) {
        return i;
      }
    }

    return _list.length;
  }

  void close() {
    _cursor?.close();
  }

  get hasMore {
    return _hasMore;
  }

  get length {
    return _list.length;
  }

  List<Flower> get data {
    return List.unmodifiable(_list);
  }

  Future<QueryCursor> _createCursor() async {
    final db = await DB.get();

    final sql = _getSql();

    return await db.sqlite.rawQueryCursor(sql, null);
  }

  String _getSql() {
    const flowerTableName = Flower.tableName;
    const nextMaintenanceTimeTableName = NextMaintenanceTime.tableName;
    String sql;
    final String sort;
    switch (_sortType) {
      case FlowerListSortType.nextMaintenanceDesc:
        sql = 'SELECT * , MIN(IFNULL(nt.nextTime, 0xffffffff)) as sortKey '
            'FROM $flowerTableName ft '
            'LEFT JOIN $nextMaintenanceTimeTableName nt '
            'on nt.flowerId = ft.id';
        sort = 'DESC';

      case FlowerListSortType.nextMaintenanceAsc:
        sql = 'SELECT * , MIN(IFNULL(nt.nextTime, 0xffffffff)) as sortKey '
            'FROM $flowerTableName ft '
            'LEFT JOIN $nextMaintenanceTimeTableName nt '
            'on nt.flowerId = ft.id';
        sort = 'ASC';

      case FlowerListSortType.companionTimeAsc:
        // 按陪伴时间升序，就是升序当前时间 - 到家时间
        sql = 'SELECT * , MIN(IFNULL(ft.arrivalTime, ft.createTime)) as sortKey '
            'FROM $flowerTableName ft';
        sort = 'DESC';

      case FlowerListSortType.companionTimeDesc:
        sql = 'SELECT * , MIN(IFNULL(ft.arrivalTime, ft.createTime)) as sortKey '
            'FROM $flowerTableName ft';
        sort = 'ASC';

      case FlowerListSortType.nameAsc:
        sql = 'SELECT *, ft.normalizedName as sortKey '
            'FROM $flowerTableName ft';
        sort = 'DESC';

      case FlowerListSortType.nameDesc:
        sql = 'SELECT *, ft.normalizedName as sortKey '
            'FROM $flowerTableName ft';
        sort = 'ASC';
    }

    sql += ' WHERE archiveTime is NULL';

    if (currentTag.id != allTag.id) {
      sql += ' and ft.id in (SELECT flowerId FROM ${TagInfoMapping.tableName} WHERE tagId = ${currentTag.id})';
    }

    sql += ' GROUP BY ft.id '
        'ORDER BY sortKey $sort';

    return sql;
  }
}
