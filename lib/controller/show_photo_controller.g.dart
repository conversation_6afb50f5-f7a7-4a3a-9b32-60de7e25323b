// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'show_photo_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$showPhotoControllerHash() =>
    r'fd9bdccfabe974f3ab92049522244a8e42a88043';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [showPhotoController].
@ProviderFor(showPhotoController)
const showPhotoControllerProvider = ShowPhotoControllerFamily();

/// See also [showPhotoController].
class ShowPhotoControllerFamily extends Family<ShowPhotoController> {
  /// See also [showPhotoController].
  const ShowPhotoControllerFamily();

  /// See also [showPhotoController].
  ShowPhotoControllerProvider call(
    int flowerId,
    List<FlowerTimelineItem> flowerTimelines,
    PhotoRecord currentPhotoRecord,
  ) {
    return ShowPhotoControllerProvider(
      flowerId,
      flowerTimelines,
      currentPhotoRecord,
    );
  }

  @override
  ShowPhotoControllerProvider getProviderOverride(
    covariant ShowPhotoControllerProvider provider,
  ) {
    return call(
      provider.flowerId,
      provider.flowerTimelines,
      provider.currentPhotoRecord,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'showPhotoControllerProvider';
}

/// See also [showPhotoController].
class ShowPhotoControllerProvider
    extends AutoDisposeProvider<ShowPhotoController> {
  /// See also [showPhotoController].
  ShowPhotoControllerProvider(
    int flowerId,
    List<FlowerTimelineItem> flowerTimelines,
    PhotoRecord currentPhotoRecord,
  ) : this._internal(
          (ref) => showPhotoController(
            ref as ShowPhotoControllerRef,
            flowerId,
            flowerTimelines,
            currentPhotoRecord,
          ),
          from: showPhotoControllerProvider,
          name: r'showPhotoControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$showPhotoControllerHash,
          dependencies: ShowPhotoControllerFamily._dependencies,
          allTransitiveDependencies:
              ShowPhotoControllerFamily._allTransitiveDependencies,
          flowerId: flowerId,
          flowerTimelines: flowerTimelines,
          currentPhotoRecord: currentPhotoRecord,
        );

  ShowPhotoControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.flowerId,
    required this.flowerTimelines,
    required this.currentPhotoRecord,
  }) : super.internal();

  final int flowerId;
  final List<FlowerTimelineItem> flowerTimelines;
  final PhotoRecord currentPhotoRecord;

  @override
  Override overrideWith(
    ShowPhotoController Function(ShowPhotoControllerRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: ShowPhotoControllerProvider._internal(
        (ref) => create(ref as ShowPhotoControllerRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        flowerId: flowerId,
        flowerTimelines: flowerTimelines,
        currentPhotoRecord: currentPhotoRecord,
      ),
    );
  }

  @override
  AutoDisposeProviderElement<ShowPhotoController> createElement() {
    return _ShowPhotoControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is ShowPhotoControllerProvider &&
        other.flowerId == flowerId &&
        other.flowerTimelines == flowerTimelines &&
        other.currentPhotoRecord == currentPhotoRecord;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, flowerId.hashCode);
    hash = _SystemHash.combine(hash, flowerTimelines.hashCode);
    hash = _SystemHash.combine(hash, currentPhotoRecord.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin ShowPhotoControllerRef on AutoDisposeProviderRef<ShowPhotoController> {
  /// The parameter `flowerId` of this provider.
  int get flowerId;

  /// The parameter `flowerTimelines` of this provider.
  List<FlowerTimelineItem> get flowerTimelines;

  /// The parameter `currentPhotoRecord` of this provider.
  PhotoRecord get currentPhotoRecord;
}

class _ShowPhotoControllerProviderElement
    extends AutoDisposeProviderElement<ShowPhotoController>
    with ShowPhotoControllerRef {
  _ShowPhotoControllerProviderElement(super.provider);

  @override
  int get flowerId => (origin as ShowPhotoControllerProvider).flowerId;
  @override
  List<FlowerTimelineItem> get flowerTimelines =>
      (origin as ShowPhotoControllerProvider).flowerTimelines;
  @override
  PhotoRecord get currentPhotoRecord =>
      (origin as ShowPhotoControllerProvider).currentPhotoRecord;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
