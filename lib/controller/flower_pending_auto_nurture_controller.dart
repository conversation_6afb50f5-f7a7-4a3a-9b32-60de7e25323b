import 'package:flower_timemachine/models/flower.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'flower_pending_auto_nurture_controller.g.dart';


@riverpod
class FlowerPendingAutoNurtureController extends _$FlowerPendingAutoNurtureController {
  @override
  List<Flower> build() {
    return [];
  }

  void add(Flower flower) {
    state = [...state, flower];
  }

  void remove(Flower flower) {
    final oldList = state;
    state = [
      for (final f in oldList)
        if (flower.id != f.id) f
    ];
  }

  void clear() {
    state = [];
  }

  bool needAutoNurtureFlower() {
    return state.isNotEmpty;
  }

  List<Flower> get() => state;

  void checkAll(List<Flower> flowers) {
    if (state.length < flowers.length) {
      state = [...flowers];
    } else {
      state = [];
    }
  }
}