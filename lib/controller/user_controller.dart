import 'package:flower_timemachine/common/secure_storage.dart';
import 'package:uuid/uuid.dart';

class UserController {
  late bool _isVip;
  late String _userID;

  static UserController? _instance;

  UserController._();

  factory UserController() {
    _instance ??= UserController._();
    return _instance!;
  }

  static UserController get() {
    return _instance!;
  }

  bool isVip() => _isVip;
  // bool isVip() => true;

  Future<void> setVip(bool state) async {
    _isVip = state;

    final storage = SecureStorage.getInstance();
    await storage.write(key: "app_vip", value: state.toString());
  }

  Future<void> initUser() async {
    final storage = SecureStorage.getInstance();

    // 初始化 user id
    String? userId = await storage.read(key: "user_id");
    if (userId != null) {
      _userID = userId;
    } else {
      // 没有创建一个
      final newUserId = const Uuid().v4();
      _userID = newUserId;
      await storage.write(key: "user_id", value: newUserId);
    }

    // 初始化 vip 属性
    String? isVip = await storage.read(key: "app_vip");
    if (isVip != null && bool.parse(isVip, caseSensitive: false)) {
      _isVip = true;
    } else {
      _isVip = false;
    }
  }

  String getID() => _userID;
}
