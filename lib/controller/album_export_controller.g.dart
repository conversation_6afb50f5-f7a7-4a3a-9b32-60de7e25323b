// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'album_export_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$albumExportControllerHash() =>
    r'ab5c8910cb8a489fb27a7b6dabcce47459fcdf6b';

/// See also [AlbumExportController].
@ProviderFor(AlbumExportController)
final albumExportControllerProvider = AutoDisposeNotifierProvider<
    AlbumExportController, List<PhotoRecord>>.internal(
  AlbumExportController.new,
  name: r'albumExportControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$albumExportControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AlbumExportController = AutoDisposeNotifier<List<PhotoRecord>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
