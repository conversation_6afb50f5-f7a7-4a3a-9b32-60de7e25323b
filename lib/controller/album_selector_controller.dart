import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/tag_info.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'album_selector_controller.g.dart';
part 'album_selector_controller.freezed.dart';

enum AlbumFilteDataType {
  flower,
  tag,
}

@freezed
class AlbumFilterData with _$AlbumFilterData {
  const factory AlbumFilterData({
    required int id,
    required String name,
    required AlbumFilteDataType type,
  }) = _AlbumFilterData;
}

@riverpod
Future<List<AlbumFilterData>> queryAlbumFilterData(
  QueryAlbumFilterDataRef ref,
) async {
  final flowers = await Flower.getAllFlowers();
  final tags = await TagInfo.getAllSorted();
  return [
    ...flowers.map((e) => AlbumFilterData(id: e.id, name: e.name, type: AlbumFilteDataType.flower)),
    ...tags.map((e) => AlbumFilterData(id: e.id, name: "#${e.name}", type: AlbumFilteDataType.tag)),
  ];
}

@Riverpod(keepAlive: true)
class AlbumCurrentFilter extends _$AlbumCurrentFilter {
  @override
  List<AlbumFilterData> build() {
    return [];
  }

  void add(AlbumFilterData filter) {
    state = [...state, filter];
  }

  void remove(AlbumFilterData filter) {
    state = [
      for (final item in state)
        if (item != filter) item,
    ];
  }

  void toggle(AlbumFilterData filter) {
    if (state.contains(filter)) {
      remove(filter);
    } else {
      add(filter);
    }
  }

  void clear() {
    state = [];
  }
}
