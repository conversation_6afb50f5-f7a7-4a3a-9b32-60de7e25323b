import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/models/tag_info_mapping.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import '../models/tag_info.dart';

part 'tag_list_controller.g.dart';

final allTag = TagInfo(id: 0xffffffff, name: 'all');

final currentTagProvider = StateProvider<TagInfo>((ref) => allTag);

@riverpod
class TagListController extends _$TagListController {
  @override
  FutureOr<List<TagInfo>> build() async {
    final ret = await TagInfo.getAllSorted();
    return ret.toList();
  }

  Future<TagInfo> add(String name) async {
    final newTag = await TagInfo.create(name, sortOrder: state.requireValue.length);
    state = AsyncValue.data([...state.requireValue, newTag]);

    return newTag;
  }

  Future<void> delete(TagInfo tag) async {
    await TagInfoMapping.deleteByTag(tag.id);
    await tag.delete();

    final list = state.requireValue;
    state = AsyncValue.data([
      for (final curTag in list)
        if (curTag.id != tag.id) curTag
    ]);

    await TagInfo.updateSortOrders(state.requireValue);
  }

  Future<void> changeTagName(TagInfo tag, String newName) async {
    if (tag.name == newName) {
      return;
    }

    final newTag = await tag.changeName(newName);

    final list = state.requireValue;
    state = AsyncValue.data([
      for (final curTag in list)
        if (curTag.id == tag.id) newTag else curTag
    ]);
  }

  Future<bool> checkSame(String name) async {
    return await TagInfo.checkSameTag(name);
  }

  Future<void> reorderTags(int oldIndex, int newIndex) async {
    final list = state.requireValue;
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final item = list.removeAt(oldIndex);
    list.insert(newIndex, item);

    TagInfo.updateSortOrders(list);

    state = AsyncValue.data(list);
  }

  bool getNoVipLimit() {
    final installTime = ShareConfig.getAppInstallTime();
    final currentTagNum = state.requireValue.length;

    if (installTime > 1704124800000) {
      // 新用户限制 5 个
      return currentTagNum >= 5;
    } else {
      // 老用户不限制
      return false;
    }
  }
}
