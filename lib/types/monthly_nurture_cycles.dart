import 'package:flower_timemachine/models/nurture_types.dart';

/// 月份养护周期数据类型
/// 用于存储每个养护类型的月份周期设置
class MonthlyNurtureCycles {
  /// Map<typeId, Map<month, cycle>>
  /// typeId: 养护类型ID
  /// month: 月份 (1-12)
  /// cycle: 周期天数
  final Map<int, Map<int, int>> _cycles;

  MonthlyNurtureCycles([Map<int, Map<int, int>>? cycles]) 
      : _cycles = cycles ?? <int, Map<int, int>>{};

  /// 从现有的 Map 创建实例
  factory MonthlyNurtureCycles.fromMap(Map<int, Map<int, int>> cycles) {
    return MonthlyNurtureCycles(Map.from(cycles.map((key, value) => 
        MapEntry(key, Map<int, int>.from(value)))));
  }

  /// 获取指定类型的月份周期设置
  Map<int, int> getCyclesForType(int typeId) {
    return Map<int, int>.from(_cycles[typeId] ?? <int, int>{});
  }

  /// 设置指定类型的月份周期
  void setCyclesForType(int typeId, Map<int, int> cycles) {
    if (cycles.isEmpty) {
      _cycles.remove(typeId);
    } else {
      _cycles[typeId] = Map<int, int>.from(cycles);
    }
  }

  /// 获取指定类型和月份的周期
  int? getCycleForMonth(int typeId, int month) {
    return _cycles[typeId]?[month];
  }

  /// 设置指定类型和月份的周期
  void setCycleForMonth(int typeId, int month, int cycle) {
    if (cycle <= 0) {
      _cycles[typeId]?.remove(month);
      if (_cycles[typeId]?.isEmpty ?? false) {
        _cycles.remove(typeId);
      }
    } else {
      _cycles[typeId] ??= <int, int>{};
      _cycles[typeId]![month] = cycle;
    }
  }

  /// 获取指定类型在指定月份的有效周期（考虑继承逻辑）
  int getEffectiveCycleForMonth(int typeId, int month, NurtureType nurtureType) {
    final typeCycles = _cycles[typeId];
    if (typeCycles == null) {
      return nurtureType.defaultCycle;
    }

    // 如果当前月份有设置，直接返回
    if (typeCycles.containsKey(month)) {
      return typeCycles[month]!;
    }

    // 只往前查找最近的月份设置（不跨年）
    for (int i = month - 1; i >= 1; i--) {
      if (typeCycles.containsKey(i)) {
        return typeCycles[i]!;
      }
    }

    // 前面没有设置，使用默认周期
    return nurtureType.defaultCycle;
  }

  /// 检查是否为空
  bool get isEmpty => _cycles.isEmpty;

  /// 检查是否不为空
  bool get isNotEmpty => _cycles.isNotEmpty;

  /// 获取所有类型ID
  Iterable<int> get typeIds => _cycles.keys;

  /// 转换为原始的 Map 格式（用于向后兼容）
  Map<int, Map<int, int>> toMap() {
    return Map.from(_cycles.map((key, value) => 
        MapEntry(key, Map<int, int>.from(value))));
  }

  /// 复制实例
  MonthlyNurtureCycles copy() {
    return MonthlyNurtureCycles.fromMap(_cycles);
  }

  /// 清空所有数据
  void clear() {
    _cycles.clear();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! MonthlyNurtureCycles) return false;
    
    if (_cycles.length != other._cycles.length) return false;
    
    for (final entry in _cycles.entries) {
      final otherValue = other._cycles[entry.key];
      if (otherValue == null) return false;
      if (entry.value.length != otherValue.length) return false;
      
      for (final cycleEntry in entry.value.entries) {
        if (otherValue[cycleEntry.key] != cycleEntry.value) return false;
      }
    }
    
    return true;
  }

  @override
  int get hashCode => _cycles.hashCode;

  @override
  String toString() {
    return 'MonthlyNurtureCycles($_cycles)';
  }
}
