// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'flower_list_controller_event.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FlowerListEventType {
  Flower get flower => throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Flower flower) addFlower,
    required TResult Function(Flower flower) deleteFlower,
    required TResult Function(Flower flower) moveFlower,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Flower flower)? addFlower,
    TResult? Function(Flower flower)? deleteFlower,
    TResult? Function(Flower flower)? moveFlower,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Flower flower)? addFlower,
    TResult Function(Flower flower)? deleteFlower,
    TResult Function(Flower flower)? moveFlower,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddFlower value) addFlower,
    required TResult Function(_DeleteFlower value) deleteFlower,
    required TResult Function(_MoveFlower value) moveFlower,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddFlower value)? addFlower,
    TResult? Function(_DeleteFlower value)? deleteFlower,
    TResult? Function(_MoveFlower value)? moveFlower,
  }) =>
      throw _privateConstructorUsedError;
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddFlower value)? addFlower,
    TResult Function(_DeleteFlower value)? deleteFlower,
    TResult Function(_MoveFlower value)? moveFlower,
    required TResult orElse(),
  }) =>
      throw _privateConstructorUsedError;

  /// Create a copy of FlowerListEventType
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FlowerListEventTypeCopyWith<FlowerListEventType> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FlowerListEventTypeCopyWith<$Res> {
  factory $FlowerListEventTypeCopyWith(
          FlowerListEventType value, $Res Function(FlowerListEventType) then) =
      _$FlowerListEventTypeCopyWithImpl<$Res, FlowerListEventType>;
  @useResult
  $Res call({Flower flower});
}

/// @nodoc
class _$FlowerListEventTypeCopyWithImpl<$Res, $Val extends FlowerListEventType>
    implements $FlowerListEventTypeCopyWith<$Res> {
  _$FlowerListEventTypeCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FlowerListEventType
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flower = null,
  }) {
    return _then(_value.copyWith(
      flower: null == flower
          ? _value.flower
          : flower // ignore: cast_nullable_to_non_nullable
              as Flower,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddFlowerImplCopyWith<$Res>
    implements $FlowerListEventTypeCopyWith<$Res> {
  factory _$$AddFlowerImplCopyWith(
          _$AddFlowerImpl value, $Res Function(_$AddFlowerImpl) then) =
      __$$AddFlowerImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Flower flower});
}

/// @nodoc
class __$$AddFlowerImplCopyWithImpl<$Res>
    extends _$FlowerListEventTypeCopyWithImpl<$Res, _$AddFlowerImpl>
    implements _$$AddFlowerImplCopyWith<$Res> {
  __$$AddFlowerImplCopyWithImpl(
      _$AddFlowerImpl _value, $Res Function(_$AddFlowerImpl) _then)
      : super(_value, _then);

  /// Create a copy of FlowerListEventType
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flower = null,
  }) {
    return _then(_$AddFlowerImpl(
      null == flower
          ? _value.flower
          : flower // ignore: cast_nullable_to_non_nullable
              as Flower,
    ));
  }
}

/// @nodoc

class _$AddFlowerImpl implements _AddFlower {
  const _$AddFlowerImpl(this.flower);

  @override
  final Flower flower;

  @override
  String toString() {
    return 'FlowerListEventType.addFlower(flower: $flower)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddFlowerImpl &&
            (identical(other.flower, flower) || other.flower == flower));
  }

  @override
  int get hashCode => Object.hash(runtimeType, flower);

  /// Create a copy of FlowerListEventType
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddFlowerImplCopyWith<_$AddFlowerImpl> get copyWith =>
      __$$AddFlowerImplCopyWithImpl<_$AddFlowerImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Flower flower) addFlower,
    required TResult Function(Flower flower) deleteFlower,
    required TResult Function(Flower flower) moveFlower,
  }) {
    return addFlower(flower);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Flower flower)? addFlower,
    TResult? Function(Flower flower)? deleteFlower,
    TResult? Function(Flower flower)? moveFlower,
  }) {
    return addFlower?.call(flower);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Flower flower)? addFlower,
    TResult Function(Flower flower)? deleteFlower,
    TResult Function(Flower flower)? moveFlower,
    required TResult orElse(),
  }) {
    if (addFlower != null) {
      return addFlower(flower);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddFlower value) addFlower,
    required TResult Function(_DeleteFlower value) deleteFlower,
    required TResult Function(_MoveFlower value) moveFlower,
  }) {
    return addFlower(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddFlower value)? addFlower,
    TResult? Function(_DeleteFlower value)? deleteFlower,
    TResult? Function(_MoveFlower value)? moveFlower,
  }) {
    return addFlower?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddFlower value)? addFlower,
    TResult Function(_DeleteFlower value)? deleteFlower,
    TResult Function(_MoveFlower value)? moveFlower,
    required TResult orElse(),
  }) {
    if (addFlower != null) {
      return addFlower(this);
    }
    return orElse();
  }
}

abstract class _AddFlower implements FlowerListEventType {
  const factory _AddFlower(final Flower flower) = _$AddFlowerImpl;

  @override
  Flower get flower;

  /// Create a copy of FlowerListEventType
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddFlowerImplCopyWith<_$AddFlowerImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$DeleteFlowerImplCopyWith<$Res>
    implements $FlowerListEventTypeCopyWith<$Res> {
  factory _$$DeleteFlowerImplCopyWith(
          _$DeleteFlowerImpl value, $Res Function(_$DeleteFlowerImpl) then) =
      __$$DeleteFlowerImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Flower flower});
}

/// @nodoc
class __$$DeleteFlowerImplCopyWithImpl<$Res>
    extends _$FlowerListEventTypeCopyWithImpl<$Res, _$DeleteFlowerImpl>
    implements _$$DeleteFlowerImplCopyWith<$Res> {
  __$$DeleteFlowerImplCopyWithImpl(
      _$DeleteFlowerImpl _value, $Res Function(_$DeleteFlowerImpl) _then)
      : super(_value, _then);

  /// Create a copy of FlowerListEventType
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flower = null,
  }) {
    return _then(_$DeleteFlowerImpl(
      null == flower
          ? _value.flower
          : flower // ignore: cast_nullable_to_non_nullable
              as Flower,
    ));
  }
}

/// @nodoc

class _$DeleteFlowerImpl implements _DeleteFlower {
  const _$DeleteFlowerImpl(this.flower);

  @override
  final Flower flower;

  @override
  String toString() {
    return 'FlowerListEventType.deleteFlower(flower: $flower)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$DeleteFlowerImpl &&
            (identical(other.flower, flower) || other.flower == flower));
  }

  @override
  int get hashCode => Object.hash(runtimeType, flower);

  /// Create a copy of FlowerListEventType
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$DeleteFlowerImplCopyWith<_$DeleteFlowerImpl> get copyWith =>
      __$$DeleteFlowerImplCopyWithImpl<_$DeleteFlowerImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Flower flower) addFlower,
    required TResult Function(Flower flower) deleteFlower,
    required TResult Function(Flower flower) moveFlower,
  }) {
    return deleteFlower(flower);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Flower flower)? addFlower,
    TResult? Function(Flower flower)? deleteFlower,
    TResult? Function(Flower flower)? moveFlower,
  }) {
    return deleteFlower?.call(flower);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Flower flower)? addFlower,
    TResult Function(Flower flower)? deleteFlower,
    TResult Function(Flower flower)? moveFlower,
    required TResult orElse(),
  }) {
    if (deleteFlower != null) {
      return deleteFlower(flower);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddFlower value) addFlower,
    required TResult Function(_DeleteFlower value) deleteFlower,
    required TResult Function(_MoveFlower value) moveFlower,
  }) {
    return deleteFlower(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddFlower value)? addFlower,
    TResult? Function(_DeleteFlower value)? deleteFlower,
    TResult? Function(_MoveFlower value)? moveFlower,
  }) {
    return deleteFlower?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddFlower value)? addFlower,
    TResult Function(_DeleteFlower value)? deleteFlower,
    TResult Function(_MoveFlower value)? moveFlower,
    required TResult orElse(),
  }) {
    if (deleteFlower != null) {
      return deleteFlower(this);
    }
    return orElse();
  }
}

abstract class _DeleteFlower implements FlowerListEventType {
  const factory _DeleteFlower(final Flower flower) = _$DeleteFlowerImpl;

  @override
  Flower get flower;

  /// Create a copy of FlowerListEventType
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$DeleteFlowerImplCopyWith<_$DeleteFlowerImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class _$$MoveFlowerImplCopyWith<$Res>
    implements $FlowerListEventTypeCopyWith<$Res> {
  factory _$$MoveFlowerImplCopyWith(
          _$MoveFlowerImpl value, $Res Function(_$MoveFlowerImpl) then) =
      __$$MoveFlowerImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({Flower flower});
}

/// @nodoc
class __$$MoveFlowerImplCopyWithImpl<$Res>
    extends _$FlowerListEventTypeCopyWithImpl<$Res, _$MoveFlowerImpl>
    implements _$$MoveFlowerImplCopyWith<$Res> {
  __$$MoveFlowerImplCopyWithImpl(
      _$MoveFlowerImpl _value, $Res Function(_$MoveFlowerImpl) _then)
      : super(_value, _then);

  /// Create a copy of FlowerListEventType
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? flower = null,
  }) {
    return _then(_$MoveFlowerImpl(
      null == flower
          ? _value.flower
          : flower // ignore: cast_nullable_to_non_nullable
              as Flower,
    ));
  }
}

/// @nodoc

class _$MoveFlowerImpl implements _MoveFlower {
  const _$MoveFlowerImpl(this.flower);

  @override
  final Flower flower;

  @override
  String toString() {
    return 'FlowerListEventType.moveFlower(flower: $flower)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$MoveFlowerImpl &&
            (identical(other.flower, flower) || other.flower == flower));
  }

  @override
  int get hashCode => Object.hash(runtimeType, flower);

  /// Create a copy of FlowerListEventType
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$MoveFlowerImplCopyWith<_$MoveFlowerImpl> get copyWith =>
      __$$MoveFlowerImplCopyWithImpl<_$MoveFlowerImpl>(this, _$identity);

  @override
  @optionalTypeArgs
  TResult when<TResult extends Object?>({
    required TResult Function(Flower flower) addFlower,
    required TResult Function(Flower flower) deleteFlower,
    required TResult Function(Flower flower) moveFlower,
  }) {
    return moveFlower(flower);
  }

  @override
  @optionalTypeArgs
  TResult? whenOrNull<TResult extends Object?>({
    TResult? Function(Flower flower)? addFlower,
    TResult? Function(Flower flower)? deleteFlower,
    TResult? Function(Flower flower)? moveFlower,
  }) {
    return moveFlower?.call(flower);
  }

  @override
  @optionalTypeArgs
  TResult maybeWhen<TResult extends Object?>({
    TResult Function(Flower flower)? addFlower,
    TResult Function(Flower flower)? deleteFlower,
    TResult Function(Flower flower)? moveFlower,
    required TResult orElse(),
  }) {
    if (moveFlower != null) {
      return moveFlower(flower);
    }
    return orElse();
  }

  @override
  @optionalTypeArgs
  TResult map<TResult extends Object?>({
    required TResult Function(_AddFlower value) addFlower,
    required TResult Function(_DeleteFlower value) deleteFlower,
    required TResult Function(_MoveFlower value) moveFlower,
  }) {
    return moveFlower(this);
  }

  @override
  @optionalTypeArgs
  TResult? mapOrNull<TResult extends Object?>({
    TResult? Function(_AddFlower value)? addFlower,
    TResult? Function(_DeleteFlower value)? deleteFlower,
    TResult? Function(_MoveFlower value)? moveFlower,
  }) {
    return moveFlower?.call(this);
  }

  @override
  @optionalTypeArgs
  TResult maybeMap<TResult extends Object?>({
    TResult Function(_AddFlower value)? addFlower,
    TResult Function(_DeleteFlower value)? deleteFlower,
    TResult Function(_MoveFlower value)? moveFlower,
    required TResult orElse(),
  }) {
    if (moveFlower != null) {
      return moveFlower(this);
    }
    return orElse();
  }
}

abstract class _MoveFlower implements FlowerListEventType {
  const factory _MoveFlower(final Flower flower) = _$MoveFlowerImpl;

  @override
  Flower get flower;

  /// Create a copy of FlowerListEventType
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$MoveFlowerImplCopyWith<_$MoveFlowerImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
