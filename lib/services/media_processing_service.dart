import 'dart:io';

import 'package:flower_timemachine/models/media_item.dart';
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:get_thumbnail_video/index.dart';
import 'package:get_thumbnail_video/video_thumbnail.dart';
import 'package:path_provider/path_provider.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:uuid/uuid.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart';

class MediaProcessingService {
  static final MediaProcessingService _instance = MediaProcessingService._internal();

  factory MediaProcessingService() => _instance;

  MediaProcessingService._internal();

  static Future<XFile?> getVideoThumbnail(String path) async {
    return await VideoThumbnail.thumbnailFile(
      video: path,
      imageFormat: ImageFormat.JPEG,
      maxWidth: 0,
      maxHeight: 0,
      quality: 80,
    );
  }


  static Future<XFile?> getImageThumbnail(String path) async {
    const uuid = Uuid();
    final tempDir = await getTemporaryDirectory();
    String tempFilePath = "${tempDir.path}${Platform.pathSeparator}${uuid.v4()}.jpg";

    return await FlutterImageCompress.compressAndGetFile(
      path,
      tempFilePath,
      quality: 80,
    );
  }

  /// 处理选中的媒体资源
  ///
  /// [assets] 选中的资源列表
  /// 返回处理后的 PhotoRecord 列表
  Future<List<MediaItem>> processSelectedAssets(List<AssetEntity> assets) async {
    List<MediaItem> result = [];

    for (final asset in assets) {
      final file = await asset.file;
      if (file != null) {
        if (asset.type == AssetType.video) {
          final thumbnailPath = await getVideoThumbnail(file.path);
          if (thumbnailPath != null) {
            result.add(MediaItem(
              path: file.path,
              thumbnailPath: thumbnailPath.path,
              type: MediaType.video,
            ));
          }
          continue;
        }

        if (asset.type != AssetType.image) {
          continue;
        }

        final livePhotoVideo = await asset.fileWithSubtype;
        if (!asset.isLivePhoto || livePhotoVideo == null) {
          result.add(MediaItem(
              path: file.path,
              thumbnailPath: null, type: MediaType.image
          ));
          continue;
        }

        final thumbnailPath = await getImageThumbnail(file.path);
        result.add(MediaItem(
          path: livePhotoVideo.path,
          thumbnailPath: thumbnailPath?.path,
          type: MediaType.livePhoto,
        ));
      }
    }

    return result;
  }
}
