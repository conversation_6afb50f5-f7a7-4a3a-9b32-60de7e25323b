import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'dart:io';

import 'video_player_widget.dart';

class MediaRenderer extends StatelessWidget {
  const MediaRenderer({
    super.key,
    required this.path,
    required this.mediaType,
    this.isVisible = true,
  });

  final String path;
  final MediaType mediaType;
  final bool isVisible;

  @override
  Widget build(BuildContext context) {
    switch (mediaType) {
      case MediaType.image:
        return _ImageMedia(path: path);
      case MediaType.video:
        return _VideoMedia(
          path: path,
          autoPlay: isVisible,
          showControls: true,
          looping: false,
          isVisible: isVisible,
        );
      case MediaType.livePhoto:
        return _VideoMedia(
          path: path,
          autoPlay: isVisible,
          showControls: false,
          looping: false,
          muted: true,
          isVisible: isVisible,
        );
    }
  }
}

class _ImageMedia extends StatelessWidget {
  const _ImageMedia({required this.path});

  final String path;

  @override
  Widget build(BuildContext context) {
    return PhotoView(
      imageProvider: FileImage(File(path)),
      minScale: PhotoViewComputedScale.contained,
      maxScale: PhotoViewComputedScale.covered * 2,
      backgroundDecoration: const BoxDecoration(color: Colors.black),
      loadingBuilder: (context, event) => const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }
}

class _VideoMedia extends StatelessWidget {
  const _VideoMedia({
    required this.path,
    this.autoPlay = false,
    this.showControls = true,
    this.looping = false,
    this.muted = false,
    this.isVisible = true,
  });

  final String path;
  final bool autoPlay;
  final bool showControls;
  final bool looping;
  final bool muted;
  final bool isVisible;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black,
      child: Center(
        child: VideoPlayerWidget(
          key: ValueKey(path),
          videoPath: path,
          autoPlay: autoPlay && isVisible,
          showControls: showControls,
          looping: looping,
          muted: muted,
          isVisible: isVisible,
        ),
      ),
    );
  }
}
