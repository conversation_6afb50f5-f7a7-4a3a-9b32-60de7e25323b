// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'nurture_dialog.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$NurtureDialogResult {
  DateTime get date => throw _privateConstructorUsedError;
  String get remark => throw _privateConstructorUsedError;

  /// Create a copy of NurtureDialogResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $NurtureDialogResultCopyWith<NurtureDialogResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $NurtureDialogResultCopyWith<$Res> {
  factory $NurtureDialogResultCopyWith(
          NurtureDialogResult value, $Res Function(NurtureDialogResult) then) =
      _$NurtureDialogResultCopyWithImpl<$Res, NurtureDialogResult>;
  @useResult
  $Res call({DateTime date, String remark});
}

/// @nodoc
class _$NurtureDialogResultCopyWithImpl<$Res, $Val extends NurtureDialogResult>
    implements $NurtureDialogResultCopyWith<$Res> {
  _$NurtureDialogResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of NurtureDialogResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? remark = null,
  }) {
    return _then(_value.copyWith(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      remark: null == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$NurtureDialogResultImplCopyWith<$Res>
    implements $NurtureDialogResultCopyWith<$Res> {
  factory _$$NurtureDialogResultImplCopyWith(_$NurtureDialogResultImpl value,
          $Res Function(_$NurtureDialogResultImpl) then) =
      __$$NurtureDialogResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({DateTime date, String remark});
}

/// @nodoc
class __$$NurtureDialogResultImplCopyWithImpl<$Res>
    extends _$NurtureDialogResultCopyWithImpl<$Res, _$NurtureDialogResultImpl>
    implements _$$NurtureDialogResultImplCopyWith<$Res> {
  __$$NurtureDialogResultImplCopyWithImpl(_$NurtureDialogResultImpl _value,
      $Res Function(_$NurtureDialogResultImpl) _then)
      : super(_value, _then);

  /// Create a copy of NurtureDialogResult
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? date = null,
    Object? remark = null,
  }) {
    return _then(_$NurtureDialogResultImpl(
      date: null == date
          ? _value.date
          : date // ignore: cast_nullable_to_non_nullable
              as DateTime,
      remark: null == remark
          ? _value.remark
          : remark // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

class _$NurtureDialogResultImpl implements _NurtureDialogResult {
  const _$NurtureDialogResultImpl({required this.date, required this.remark});

  @override
  final DateTime date;
  @override
  final String remark;

  @override
  String toString() {
    return 'NurtureDialogResult(date: $date, remark: $remark)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$NurtureDialogResultImpl &&
            (identical(other.date, date) || other.date == date) &&
            (identical(other.remark, remark) || other.remark == remark));
  }

  @override
  int get hashCode => Object.hash(runtimeType, date, remark);

  /// Create a copy of NurtureDialogResult
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$NurtureDialogResultImplCopyWith<_$NurtureDialogResultImpl> get copyWith =>
      __$$NurtureDialogResultImplCopyWithImpl<_$NurtureDialogResultImpl>(
          this, _$identity);
}

abstract class _NurtureDialogResult implements NurtureDialogResult {
  const factory _NurtureDialogResult(
      {required final DateTime date,
      required final String remark}) = _$NurtureDialogResultImpl;

  @override
  DateTime get date;
  @override
  String get remark;

  /// Create a copy of NurtureDialogResult
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$NurtureDialogResultImplCopyWith<_$NurtureDialogResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
