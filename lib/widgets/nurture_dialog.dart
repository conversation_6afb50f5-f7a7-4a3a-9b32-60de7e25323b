import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'nurture_dialog.freezed.dart';

@freezed
class NurtureDialogResult with _$NurtureDialogResult {
  const factory NurtureDialogResult({
    required DateTime date,
    required String remark,
  }) = _NurtureDialogResult;
}

class NurtureDialog extends StatefulWidget {
  const NurtureDialog({super.key, required this.nurture});

  final String nurture;

  @override
  State<NurtureDialog> createState() => _NurtureDialogState();

  static Future<NurtureDialogResult?> show(BuildContext context, String nurture) async {
    return await showDialog<NurtureDialogResult?>(
      context: context,
      builder: (context) => NurtureDialog(nurture: nurture),
    );
  }
}

class _NurtureDialogState extends State<NurtureDialog> {
  final TextEditingController _remarkController = TextEditingController();

  DateTime _date = DateTime.now();

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 252,
            child: CalendarDatePicker(
              initialDate: _date,
              firstDate: DateTime(1999, 1, 1),
              lastDate: DateTime.now(),
              onDateChanged: (DateTime newDate) {
                _date = newDate;
              },
            ),
          ),
          SizedBox(
            height: 20,
            child: TextField(
              controller: _remarkController,
              decoration: InputDecoration(
                hintText: "remark_hint".tr(),
                border: InputBorder.none,
                hintStyle: const TextStyle(fontSize: 12),
                prefixIcon: const Icon(Icons.pending_outlined, size: 20),
                contentPadding: EdgeInsets.zero,
                isDense: true,
              ),
              style: const TextStyle(fontSize: 12),
              maxLines: 1,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: ElevatedButton(
                    onPressed: () => Navigator.pop(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.black12,
                      foregroundColor: Colors.white,
                    ),
                    child: Text('cancel'.tr()),
                  ),
                ),
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: ElevatedButton(
                    onPressed: _onDateChanged,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                    child: Text(widget.nurture),
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  void _onDateChanged() {
    final r = NurtureDialogResult(date: _date, remark: _remarkController.text);
    Navigator.pop(context, r);
  }
}
