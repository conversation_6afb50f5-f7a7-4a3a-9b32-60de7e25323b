import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

/// 自定义的时间选择器组件
class TimePickerSpinner extends StatefulWidget {
  /// 当前选择的时间
  final TimeOfDay time;

  /// 时间变化回调
  final ValueChanged<TimeOfDay> onTimeChanged;

  const TimePickerSpinner({
    super.key,
    required this.time,
    required this.onTimeChanged,
  });

  @override
  State<TimePickerSpinner> createState() => _TimePickerSpinnerState();
}

class _TimePickerSpinnerState extends State<TimePickerSpinner> {
  late int _selectedHour;
  late int _selectedMinute;

  @override
  void initState() {
    super.initState();
    _selectedHour = widget.time.hour;
    _selectedMinute = widget.time.minute;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 16.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildHourPicker(),
              const SizedBox(width: 20),
              const Text(':', style: TextStyle(fontSize: 30)),
              const SizedBox(width: 20),
              _buildMinutePicker(),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            '${_selectedHour.toString().padLeft(2, '0')}:${_selectedMinute.toString().padLeft(2, '0')}',
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildHourPicker() {
    return _buildNumberPicker(
      minValue: 0,
      maxValue: 23,
      value: _selectedHour,
      onChanged: (value) {
        setState(() {
          _selectedHour = value;
          widget.onTimeChanged(TimeOfDay(hour: _selectedHour, minute: _selectedMinute));
        });
      },
      itemCount: 3,
      itemHeight: 50,
      label: 'hour'.tr(),
    );
  }

  Widget _buildMinutePicker() {
    return _buildNumberPicker(
      minValue: 0,
      maxValue: 59,
      value: _selectedMinute,
      onChanged: (value) {
        setState(() {
          _selectedMinute = value;
          widget.onTimeChanged(TimeOfDay(hour: _selectedHour, minute: _selectedMinute));
        });
      },
      itemCount: 3,
      itemHeight: 50,
      label: 'minute'.tr(),
    );
  }

  Widget _buildNumberPicker({
    required int minValue,
    required int maxValue,
    required int value,
    required ValueChanged<int> onChanged,
    required int itemCount,
    required double itemHeight,
    required String label,
  }) {
    return Column(
      children: [
        SizedBox(
          height: itemHeight * itemCount,
          width: 60,
          child: ListWheelScrollView.useDelegate(
            controller: FixedExtentScrollController(initialItem: value),
            physics: const FixedExtentScrollPhysics(),
            itemExtent: itemHeight,
            perspective: 0.005,
            diameterRatio: 1.2,
            onSelectedItemChanged: onChanged,
            childDelegate: ListWheelChildBuilderDelegate(
              childCount: maxValue - minValue + 1,
              builder: (context, index) {
                final itemValue = minValue + index;
                return Center(
                  child: Text(
                    itemValue.toString().padLeft(2, '0'),
                    style: TextStyle(
                      fontSize: 22,
                      fontWeight: value == itemValue ? FontWeight.bold : FontWeight.normal,
                      color: value == itemValue ? Theme.of(context).primaryColor : Colors.black54,
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        const SizedBox(height: 8),
        Text(label, style: const TextStyle(fontSize: 16)),
      ],
    );
  }
}
