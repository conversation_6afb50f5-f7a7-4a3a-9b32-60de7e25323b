import 'package:flutter/material.dart';

class TagInfoContainer extends StatelessWidget {
  const TagInfoContainer({super.key, required this.tagName});

  final String tagName;

  @override
  Widget build(BuildContext context) =>
      Container(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 7),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          border: Border.all(width: 1, color: const Color(0xFF339900)),
        ),
        child: Text(tagName, style: const TextStyle(fontSize: 16))
    );
}