import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

// 项目导入
import 'package:flower_timemachine/common/date_utils.dart';
import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/common/resources.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/controller/recent_maintenance_day.dart';
import 'ftm_box.dart';

/// 花朵基本信息组件
///
/// 显示花朵的基本信息，包括名称、头像、养护提示等
class FlowerBaseInfoWidget extends StatelessWidget {
  const FlowerBaseInfoWidget({
    super.key,
    required this.flower,
    this.onTap,
    this.enableHintIcon = false,
    this.enableAutoFixIcon = false,
    this.showExistDay = false,
    this.onAutoFix,
  });

  final Flower flower;
  final GestureTapCallback? onTap;
  final bool enableHintIcon;
  final bool enableAutoFixIcon;
  final VoidCallback? onAutoFix;
  final bool showExistDay;

  @override
  Widget build(BuildContext context) {
    return FTMBox(circular: 10, child: ListenableBuilder(listenable: flower, builder: listenableBuilder));
  }

  /// 构建响应式UI
  Widget listenableBuilder(BuildContext context, Widget? child) {
    // 判断是否需要维护
    bool needMaintenance = false;

    // 构建下次任务提示
    final Text nextTaskPrompt = _buildNextTaskPrompt(needMaintenance);

    // 根据提示状态更新needMaintenance
    if (flower.nextTaskRecord != null) {
      final nextTaskRecord = flower.nextTaskRecord!;
      if (nextTaskRecord.day <= 0) {
        needMaintenance = true;
      }
    }

    // 构建尾部组件
    final List<Widget> trailingChildren = _buildTrailingChildren(context, needMaintenance);

    // 确定是否显示未来养护提示
    final isShowFutureNurture = ShareConfig.getShowFutureNurture() || needMaintenance;
    final hasLastMaintenanceTime = flower.lastMaintenanceTime != null;

    // 构建头像
    final Image avatar = _buildAvatar(isShowFutureNurture, hasLastMaintenanceTime);

    // 组合最近养护记录和下次任务提示
    final Widget subtitle = _buildSubtitle(isShowFutureNurture, nextTaskPrompt);

    // 构建主布局
    return _buildMainLayout(context, avatar, subtitle, trailingChildren);
  }

  /// 构建下次任务提示文本
  Text _buildNextTaskPrompt(bool needMaintenance) {
    if (flower.nextTaskRecord == null) {
      return const Text('nurture_reminder.no_task', style: TextStyle(color: Colors.black54)).tr();
    }

    final nextTaskRecord = flower.nextTaskRecord!;
    final String subject = taskSubject(nextTaskRecord);

    if (nextTaskRecord.day > 0) {
      return const Text('nurture_reminder.future', style: TextStyle(color: Colors.black54)).tr(
        namedArgs: {
          "day": formatDay(nextTaskRecord.day),
          "nurture": subject,
        },
      );
    } else if (nextTaskRecord.day < 0) {
      final day = -nextTaskRecord.day;
      return const Text('nurture_reminder.overdue', style: TextStyle(color: Colors.red))
          .tr(namedArgs: {"name": subject, "day": day.toString()});
    } else {
      return const Text('nurture_reminder.today', style: TextStyle(color: Colors.orange))
          .tr(namedArgs: {"nurture": subject});
    }
  }

  /// 构建尾部组件列表
  List<Widget> _buildTrailingChildren(BuildContext context, bool needMaintenance) {
    final List<Widget> children = [];

    // 添加自动修复按钮
    if (enableAutoFixIcon && needMaintenance) {
      children.add(IconButton(
        onPressed: onAutoFix,
        icon: const Icon(Icons.auto_fix_high),
        iconSize: 28,
        highlightColor: Colors.transparent,
        color: const Color(0xFF339900),
      ));
    }

    // 添加存在天数
    if (showExistDay) {
      children.add(RichText(
        text: TextSpan(children: [
          TextSpan(text: 'nurture_reminder.companion'.tr(), style: const TextStyle(color: Colors.black26)),
          TextSpan(
              text: '${flower.dayOfCreate}', style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 22)),
          TextSpan(text: 'nurture_reminder.companion_days'.tr(), style: const TextStyle(color: Colors.black26))
        ]),
      ));
      children.add(const SizedBox(width: 5));
    }

    // 添加提示图标
    if (enableHintIcon) {
      children.add(const Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Colors.black54,
      ));
    }

    return children;
  }

  /// 构建头像
  Image _buildAvatar(bool isShowFutureNurture, bool hasLastMaintenanceTime) {
    const double avatarWidth = 66;
    const double avatarHeight = 66;

    if (flower.avatar != null) {
      return Image.file(
        File(flower.avatar!),
        width: avatarWidth,
        height: avatarHeight,
        fit: BoxFit.cover,
      );
    } else {
      return Image.asset(
        R.iconFlower,
        width: avatarWidth,
        height: avatarHeight,
        fit: BoxFit.cover,
      );
    }
  }

  /// 构建副标题
  Widget _buildSubtitle(bool isShowFutureNurture, Text nextTaskPrompt) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (isShowFutureNurture && flower.archiveTime == null) nextTaskPrompt,
        if (flower.lastMaintenanceTime != null) _buildLastMaintenanceTips(isShowFutureNurture),
      ],
    );
  }

  /// 构建主布局
  Widget _buildMainLayout(BuildContext context, Image avatar, Widget subtitle, List<Widget> trailingChildren) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Padding(
        padding: const EdgeInsets.only(left: 10, right: 5, top: 8, bottom: 8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            // 头像部分带留白
            Container(
              margin: const EdgeInsets.only(right: 12),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8.0),
                child: avatar,
              ),
            ),
            // 内容部分
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: 4.0),
                    child: Text(flower.name, style: Theme.of(context).textTheme.titleMedium),
                  ),
                  subtitle,
                ],
              ),
            ),
            // 尾部部分
            if (trailingChildren.isNotEmpty)
              SizedBox(
                height: 66,
                child: Row(
                  // mainAxisSize: MainAxisSize.min,
                  children: trailingChildren,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildLastMaintenanceTips(bool isShowFutureNurture) {
    // 构建最近养护记录提示
    final lastRecords = flower.lastMaintenanceTime!;

    return Padding(
      padding: const EdgeInsets.only(top: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'nurture_reminder.last_maintenance',
            style: TextStyle(color: Colors.black54, fontSize: isShowFutureNurture ? 12 : null),
          ).tr(namedArgs: {
            "time": calcDatePrompt(lastRecords.maintenanceTime, DateTime.now()),
            "type": lastRecords.types.map((t) => t.name).join("、 "),
          })
        ],
      ),
    );
  }

  String taskSubject(RecentMaintenanceDay nextTaskRecord) {
    return nextTaskRecord.types.map((e) => e.name).join(', ');
  }

  String formatDay(int day) {
    if (day > 1) {
      return 'future_day'.tr(namedArgs: {"day": day.toString()});
    } else if (day == 1) {
      return 'tomorrow'.tr();
    } else {
      // 绝对不会来到这个分支
      return '';
    }
  }
}
