
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class FTMDatePickerDialog extends StatefulWidget {
  const FTMDatePickerDialog({
    super.key,
    required this.confirmText,
    this.defaultDate,
    required this.mode,
    this.maximumDate
  });

  final String confirmText;
  final DateTime? defaultDate;
  final CupertinoDatePickerMode mode;
  final DateTime? maximumDate;


  static Future<DateTime?> show(
      BuildContext context,
      String confirmText,
      {
        DateTime? defaultDate,
        DateTime? maximumDate,
        required CupertinoDatePickerMode mode
      }) async {
    return await showCupertinoModalPopup<DateTime?>(
      context: context,
      builder: (BuildContext context) => FTMDatePickerDialog(
        confirmText: confirmText,
        defaultDate: defaultDate,
        mode: mode,
        maximumDate: maximumDate
      ),
    );
  }

  @override
  State<StatefulWidget> createState() => _FTMDatePickerDialogState();
}

class _FTMDatePickerDialogState extends State<FTMDatePickerDialog> {
  late DateTime d;

  @override
  void initState() {
    super.initState();
    d = widget.defaultDate ?? DateTime.now();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 300,
      margin: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      color: CupertinoColors.systemBackground.resolveFrom(context),
      child: Column(
          children: [
            Padding(
                padding: const EdgeInsets.symmetric(horizontal: 3),
                child: Row(
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text('cancel'.tr(), style: TextStyle(fontSize: 16, color: Theme.of(context).primaryColor)),
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context, d);
                        },
                        child: Text(widget.confirmText,
                            style: TextStyle(fontSize: 16, color: Theme.of(context).primaryColor)
                        ),
                      ),
                    ]
                )
            ),
            Expanded(
                child: CupertinoDatePicker(
                  initialDateTime: d,
                  mode: widget.mode,
                  maximumDate: widget.maximumDate,
                  use24hFormat: true,
                  onDateTimeChanged: (DateTime newDate) => d = newDate,
                )
            )
          ]
      ),
    );
  }

}