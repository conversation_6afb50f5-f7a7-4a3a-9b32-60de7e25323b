import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:timezone/data/latest_all.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

final FlutterLocalNotificationsPlugin _notificationsPlugin = FlutterLocalNotificationsPlugin();

class FTMNotification {
  static init() async {
    const initializationSettingsAndroid = AndroidInitializationSettings('app_icon');
    const initializationSettingsDarwin =
        DarwinInitializationSettings(onDidReceiveLocalNotification: onDidReceiveLocalNotification);

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid, iOS: initializationSettingsDarwin);

    tz.initializeTimeZones();

    await _notificationsPlugin.initialize(initializationSettings);
  }

  static void sendAfter(int id, DateTime time) async {
    final t = tz.TZDateTime.from(time, tz.local);
    final androidDetail =
        AndroidNotificationDetails('1', 'care_reminder'.tr(), channelDescription: 'care_reminder'.tr());
    const iosDetail = DarwinNotificationDetails();

    await _notificationsPlugin.zonedSchedule(id, 'care_reminder'.tr(), 'care_reminder_content'.tr(), t,
        NotificationDetails(android: androidDetail, iOS: iosDetail),
        androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime);
  }

  static cancelAll() async {
    await _notificationsPlugin.cancelAll();
  }

  static cancel(int id) async {
    await _notificationsPlugin.cancel(id);
  }

  static void onDidReceiveLocalNotification(int id, String? title, String? body, String? payload) async {
    print('onDidReceiveLocalNotification');
  }
}
