import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

Future<bool?> showAlertDialog(String title, String desc, BuildContext context) async {
  return await showCupertinoModalPopup<bool>(
      context: context,
      builder: (BuildContext context) => CupertinoAlertDialog(
            title: Text(title),
            content: Text(desc),
            actions: <CupertinoDialogAction>[
              CupertinoDialogAction(
                isDefaultAction: true,
                onPressed: () => Navigator.pop(context, false),
                child: const Text('cancel').tr(),
              ),
              CupertinoDialogAction(
                isDestructiveAction: true,
                onPressed: () => Navigator.pop(context, true),
                child: const Text('confirm').tr(),
              ),
            ],
          ));
}

Future<bool?> showMessageDialog(String title, String? desc, BuildContext context) async {
  return await showCupertinoModalPopup<bool>(
      context: context,
      builder: (BuildContext context) => CupertinoAlertDialog(
            title: Text(title),
            content: desc != null ? Text(desc) : null,
            actions: <CupertinoDialogAction>[
              CupertinoDialogAction(
                isDefaultAction: true,
                onPressed: () => Navigator.pop(context, true),
                child: const Text('confirm').tr(),
              ),
            ],
          ));
}

Future<String?> showTextDialog(String title, int maxLength, BuildContext context) async {
  final textFieldController = TextEditingController();
  final GlobalKey formKey = GlobalKey<FormState>();
  const iosBackgroundColors = Color(0xffe0e0e0);
  String? result;

  await showDialog(
      context: context,
      builder: (context) => AlertDialog(
          shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(Radius.circular(20.0))),
          contentPadding: const EdgeInsets.only(left: 10, right: 10, top: 30),
          actionsAlignment: MainAxisAlignment.center,
          actionsPadding: const EdgeInsets.symmetric(vertical: 10),
          backgroundColor: iosBackgroundColors,
          content: Form(
            key: formKey,
            child: TextFormField(
              controller: textFieldController,
              decoration: InputDecoration(hintText: title),
              validator: (v) {
                final t = v!.trim();
                if (t.isEmpty) {
                  return "cannot_be_empty".tr();
                } else if (t.length > maxLength) {
                  return "characters_length_limit".tr(namedArgs: {"length": maxLength.toString()});
                }
                return null;
              },
            )
          ),
          actions: <Widget>[
            MaterialButton(
              color: iosBackgroundColors,
              elevation: 0,
              textColor: Theme.of(context).primaryColor,
              child: const Text('save').tr(),
              onPressed: () {
                if (!(formKey.currentState as FormState).validate()) {
                  return;
                }

                result = textFieldController.text;
                Navigator.pop(context);
              }
            )
          ]
      )
  );

  return result;
}