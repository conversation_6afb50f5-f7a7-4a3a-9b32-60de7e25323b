import 'package:flower_timemachine/controller/next_maintenance_time_controller.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:flower_timemachine/models/flower_monthly_nurture_cycle.dart';
import 'package:flower_timemachine/models/maintenance_record.dart';
import 'package:flower_timemachine/models/next_task_time.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class NurtureManagerController extends ChangeNotifier {
  final AutoDisposeChangeNotifierProviderRef<NurtureManagerController> ref;

  NurtureManagerController(this.ref);

  Future<void> toggleEnable(NurtureType type) async {
    await type.update(enable: !type.enable);

    notifyListeners();
  }

  Future<void> delete(NurtureType type) async {
    (await ref.read(nextMaintenanceTimeControllerProvider.future)).deleteNurtureType(type.id);
    await MaintenanceRecord.deleteByNurtureType(type.id);
    await NextMaintenanceTime.deleteByNurtureType(type.id);
    await type.delete();

    NurtureTypesController.get().del(type);
    await FlowerMonthlyNurtureCycle.deleteByNurtureType(type.id);

    notifyListeners();
  }

  void add(NurtureType nurtureType) {
    NurtureTypesController.get().add(nurtureType);
    notifyListeners();
  }

  List<NurtureType> get enableTypes => NurtureTypesController.get().enableTypes;
  List<NurtureType> get disabledTypes => NurtureTypesController.get().disabledTypes;
  List<NurtureType> get types => NurtureTypesController.get().types;
}

final nurtureManagerControllerProvider = ChangeNotifierProvider.autoDispose<NurtureManagerController>((ref) {
  return NurtureManagerController(ref);
});
