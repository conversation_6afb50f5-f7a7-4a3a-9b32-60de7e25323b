import 'package:flower_timemachine/pages/calendar/widgets/flower_info.dart';
import 'package:flutter/material.dart';
import 'package:flower_timemachine/types/calendar_type.dart';
import 'package:flutter_svg/svg.dart';

class NurtureEventItem extends StatelessWidget {
  final CalendarState event;

  const NurtureEventItem({
    super.key,
    required this.event,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // 养护类型
        Row(
          children: [
            SvgPicture.asset(event.nurture!.type.icon, width: 15, height: 15),
            const SizedBox(width: 8),
            Text(
              event.nurture!.type.name,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            )
          ],
        ),
        // 备注
        if (event.nurture?.remark != null && event.nurture!.remark!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              event.nurture!.remark!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ),
        const SizedBox(height: 8),

        // 花的信息
        FlowerInfo(flower: event.flower),
      ],
    );
  }
}
