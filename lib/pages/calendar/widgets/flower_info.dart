import 'dart:io';
import 'package:flower_timemachine/common/resources.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flutter/material.dart';

class FlowerInfo extends StatelessWidget {
  const FlowerInfo({
    super.key,
    required this.flower,
  });

  final Flower flower;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        // 花的头像
        ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: flower.avatar != null
              ? Image.file(
                  File(flower.avatar!),
                  width: 20,
                  height: 20,
                  fit: BoxFit.cover,
                )
              : Image.asset(
                  R.iconFlower,
                  width: 20,
                  height: 20,
                  fit: BoxFit.cover,
                ),
        ),
        const SizedBox(width: 8),
        // 花的名字
        Text(
          flower.name,
          style: TextStyle(
            fontSize: 14,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ],
    );
  }
}
