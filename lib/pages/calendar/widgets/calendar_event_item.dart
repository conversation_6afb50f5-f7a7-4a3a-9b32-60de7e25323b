import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:flower_timemachine/types/calendar_type.dart';

import 'timeline_event_item.dart';
import 'nurture_event_item.dart';

class CalendarEventItem extends StatelessWidget {
  final CalendarState event;

  const CalendarEventItem({
    super.key,
    required this.event,
  });

  @override
  Widget build(BuildContext context) {
    final timeFormat = DateFormat.Hm();
    final timeString = timeFormat.format(event.time);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 左侧时间
          SizedBox(
            child: Padding(
              padding: const EdgeInsets.only(top: 4.0, left: 8.0),
              child: Text(
                timeString,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black54,
                ),
              ),
            ),
          ),

          // 中间竖线和内容
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 8.0),
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(1),
                border: Border(
                  left: BorderSide(
                    width: 2,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: _buildEventContent(event),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventContent(CalendarState event) {
    switch (event.type) {
      case CalendarType.timeline:
        return TimelineEventItem(event: event);
      case CalendarType.nurture:
        return NurtureEventItem(event: event);
    }
  }
}
