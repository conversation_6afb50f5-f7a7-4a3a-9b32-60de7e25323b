import 'package:flower_timemachine/repository/calendar_repo.dart';
import 'package:flower_timemachine/types/calendar_type.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'calendar_controller.g.dart';

const _pageSize = 100;

@riverpod
class CalendarController extends _$CalendarController {
  bool _hasMore = true;
  bool _isLoading = false;
  int _offset = 0;

  void loadMore() async {
    if (_isLoading || !_hasMore) return;

    _isLoading = true;

    final repo = ref.read(calendarRepoProvider);
    final data = await repo.getCalendarEvent(day, _pageSize, _offset);
    _hasMore = data.length == _pageSize;
    _offset += data.length;

    state = AsyncData([...state.value ?? [], ...data]);

    _isLoading = false;
  }

  bool get hasMore => _hasMore;

  @override
  Future<List<CalendarState>> build(
    DateTime day,
  ) async {
    final repo = ref.watch(calendarRepoProvider);
    final data = await repo.getCalendarEvent(day, _pageSize, _offset);
    _hasMore = data.length == _pageSize;
    _offset = data.length;

    return data;
  }
}

@riverpod
Future<Map<DateTime, bool>> queryMonthMarkedDates(
  QueryMonthMarkedDatesRef ref,
  DateTime month,
) async {
  final repo = ref.read(calendarRepoProvider);
  final data = await repo.getCalendarMarkedDates(month);
  return data;
}

@riverpod
Future<bool> queryDayMarkedDates(
  QueryDayMarkedDatesRef ref,
  DateTime day,
) async {
  final month = DateTime(day.year, day.month, 1);
  final ret = await ref.watch(queryMonthMarkedDatesProvider(month).future);
  return ret[day] ?? false;
}
