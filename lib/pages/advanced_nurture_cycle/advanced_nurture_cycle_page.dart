import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/types/monthly_nurture_cycles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_picker_plus/flutter_picker_plus.dart';
import 'package:flutter_svg/flutter_svg.dart';

class AdvancedNurtureCyclePage extends StatefulWidget {
  const AdvancedNurtureCyclePage({
    super.key,
    required this.flowerId,
    this.isNewFlower = false,
    required this.monthCycles,
    required this.nurtureTypes,
  });

  final int flowerId;
  final bool isNewFlower; // 是否是新增花卉
  final MonthlyNurtureCycles monthCycles;
  final List<NurtureType> nurtureTypes;

  @override
  State<AdvancedNurtureCyclePage> createState() => _AdvancedNurtureCyclePageState();
}

class _AdvancedNurtureCyclePageState extends State<AdvancedNurtureCyclePage> {
  // 临时编辑数据
  late MonthlyNurtureCycles _editingMonthCycles;
  final List<String> _pickerData = Iterable.generate(366, (i) => '$i').toList();
  int _selectedTypeIndex = 0;

  static const List<String> _monthKeys = [
    'months.january', 'months.february', 'months.march', 'months.april',
    'months.may', 'months.june', 'months.july', 'months.august',
    'months.september', 'months.october', 'months.november', 'months.december'
  ];

  String _getMonthName(int month) {
    return _monthKeys[month - 1].tr();
  }

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  void _initializeData() {
    // 初始化编辑数据
    _editingMonthCycles = widget.monthCycles.copy();
  }

  NurtureType get _currentType => widget.nurtureTypes[_selectedTypeIndex];
  Map<int, int> get _currentMonthCycles => _editingMonthCycles.getCyclesForType(_currentType.id);
  int get _currentDefaultCycle => _currentType.defaultCycle;

  void _updateCycleForMonth(int month, int cycle) {
    setState(() {
      _editingMonthCycles.setCycleForMonth(_currentType.id, month, cycle);
    });
  }


  int _getEffectiveCycleForMonth(int month) {
    return _editingMonthCycles.getEffectiveCycleForMonth(_currentType.id, month, _currentType);
  }

  void _showCyclePicker(int month) {
    final currentCycle = _currentMonthCycles[month] ?? 0;
    final picker = Picker(
      title: Text('${_getMonthName(month)}${_currentType.name}${'advanced_nurture_cycle.cycle'.tr()}'),
      adapter: PickerDataAdapter<String>(pickerData: _pickerData),
      selecteds: [currentCycle],
      cancelText: 'cancel'.tr(),
      confirmText: 'confirm'.tr(),
      onConfirm: (picker, selected) {
        final newCycle = selected[0];
        _updateCycleForMonth(month, newCycle);
      },
    );
    picker.showModal(context);
  }

  void _onWillPop(didPop, result) async {
    if (didPop) {
      return;
    }
    if (mounted) {
      Navigator.of(context).pop(_editingMonthCycles);
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: _onWillPop,
      child: Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: Text('advanced_nurture_cycle.title'.tr()),
        elevation: 0,
      ),
      body: Column(
        children: [
          _buildTypeSelector(),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInfoCard(),
                  const SizedBox(height: 16),
                  _buildMonthlySettings(),
                ],
              ),
            ),
          ),
        ],
      ),
      ),
    );
  }

  Widget _buildTypeSelector() {
    if (widget.nurtureTypes.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: const Color(0xFFCBE0B3)),
          borderRadius: BorderRadius.circular(10),
        ),
        child: SizedBox(
          height: 60,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: widget.nurtureTypes.length,
            itemBuilder: (context, index) {
              final type = widget.nurtureTypes[index];
              final isSelected = index == _selectedTypeIndex;

              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedTypeIndex = index;
                  });
                },
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    color: isSelected ? Theme.of(context).primaryColor : Colors.transparent,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(
                        type.icon,
                        width: 20,
                        height: 20,
                        colorFilter: ColorFilter.mode(
                          isSelected ? Colors.white : Colors.black54,
                          BlendMode.srcIn,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        type.name,
                        style: TextStyle(
                          color: isSelected ? Colors.white : Colors.black54,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard() {
    if (widget.nurtureTypes.isEmpty) return const SizedBox.shrink();

    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFCBE0B3)),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                SvgPicture.asset(_currentType.icon, width: 24, height: 24),
                const SizedBox(width: 8),
                Text(
                  _currentType.name,
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'advanced_nurture_cycle.default_cycle_days'.tr(namedArgs: {'cycle': '$_currentDefaultCycle'}),
              style: const TextStyle(color: Colors.black54),
            ),
            const SizedBox(height: 8),
            Text(
              'advanced_nurture_cycle.month_inherit_description'.tr(),
              style: const TextStyle(color: Colors.black54, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMonthlySettings() {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFCBE0B3)),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'advanced_nurture_cycle.month_settings'.tr(),
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 12,
            separatorBuilder: (context, index) => const Divider(indent: 16, endIndent: 16),
            itemBuilder: (context, index) {
              final month = index + 1;
              return _buildMonthItem(month);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMonthItem(int month) {
    final monthCycles = _currentMonthCycles;
    final hasCustomSetting = monthCycles.containsKey(month);
    final effectiveCycle = _getEffectiveCycleForMonth(month);
    final customCycle = monthCycles[month];

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () => _showCyclePicker(month),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            SizedBox(
              width: 60,
              child: Text(
                _getMonthName(month),
                style: const TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (hasCustomSetting) ...[
                    Text(
                      '$customCycle天',
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    Text(
                      'advanced_nurture_cycle.custom_setting'.tr(),
                      style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 12),
                    ),
                  ] else ...[
                    Text(
                      '$effectiveCycle天',
                      style: const TextStyle(fontSize: 16, color: Colors.black54),
                    ),
                    Text(
                      effectiveCycle == _currentDefaultCycle ? 'advanced_nurture_cycle.use_default'.tr() : 'advanced_nurture_cycle.inherit_setting'.tr(),
                      style: const TextStyle(color: Colors.black38, fontSize: 12),
                    ),
                  ],
                ],
              ),
            ),
            if (hasCustomSetting)
              GestureDetector(
                onTap: () => _updateCycleForMonth(month, 0),
                child: const Icon(
                  Icons.clear,
                  color: Colors.red,
                  size: 20,
                ),
              )
            else
              const Icon(Icons.edit, color: Colors.black38),
          ],
        ),
      ),
    );
  }
}
