import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/file_utils.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flower_timemachine/pages/flower_share/widgets/photo_line.dart';
import 'package:flower_timemachine/widgets/loading_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:share_plus/share_plus.dart';

import 'widgets/photo_picker.dart';

class FlowerShare extends ConsumerStatefulWidget {
  const FlowerShare({super.key, required this.flower});

  static const String routeName = 'flower_share';

  final Flower flower;

  @override
  ConsumerState<FlowerShare> createState() => _FlowerShareState();
}

class _FlowerShareState extends ConsumerState<FlowerShare>
    with SingleTickerProviderStateMixin {

  final PageController _pageViewController = PageController();
  final GlobalKey<FlowerSharePhotoLineState> _flowerSharePhotoLineKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return PageView(
      controller: _pageViewController,
      children: [
        FlowerSharePhotoPicker(
          flower: widget.flower,
          onNext: _onNext,
        ),
        FlowerSharePhotoLine(
          key: _flowerSharePhotoLineKey,
          onShare: _onGenImage,
          onPrev: _onPrev,
        )
      ],
    );
  }

  void _onNext() {
    _pageViewController.animateToPage(
        1, duration: const Duration(milliseconds: 300), curve: Curves.ease
    );
  }

  void _onPrev() {
    _pageViewController.animateToPage(
        0, duration: const Duration(milliseconds: 300), curve: Curves.ease
    );
  }

  void _onGenImage() async {
    LoadingDialog.show();

    final imageData =
        await _flowerSharePhotoLineKey.currentState?.controller.capture();
    if (imageData == null) {
      // 关闭 loading 窗口
      LoadingDialog.dismiss();

      Fluttertoast.showToast(
          msg: "flower_share.screenshot_failed".tr(),
          toastLength: Toast.LENGTH_SHORT);
      return;
    }

    final fileName =
        'flower-${(DateTime.now().millisecondsSinceEpoch / 1000).truncate()}.png';

    final f = await writeToTempFile(imageData, fileName);

    await Share.shareXFiles([XFile(f.path)], subject: 'app_name'.tr());

    // 关闭 loading 窗口
    LoadingDialog.dismiss();

    await f.delete();
  }
}
