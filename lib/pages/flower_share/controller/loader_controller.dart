import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'loader_controller.g.dart';

@riverpod
class FlowerShareLoaderController extends _$FlowerShareLoaderController {
  late Flower _flower;
  late bool _hasMore;

  static const _limit = 100;

  @override
  Future<List<PhotoRecord>> build(Flower flower) async {
    _flower = flower;
    _hasMore = true;
    return await PhotoRecord.getByFlowerIdExcludeVideo(_flower.id, limit: _limit, timeOrder: 'DESC');
  }

  Future<void> loadMore() async {
    final currentPhotos = state.value ?? [];
    final newPhotos = await PhotoRecord.getByFlowerIdExcludeVideo(
        flower.id,
        offset: currentPhotos.length,
        limit: _limit,
        timeOrder: 'DESC'
    );

    _hasMore = newPhotos.length == _limit;

    state = AsyncValue.data([...currentPhotos, ...newPhotos]);
  }

  bool get hasMore => _hasMore;
}