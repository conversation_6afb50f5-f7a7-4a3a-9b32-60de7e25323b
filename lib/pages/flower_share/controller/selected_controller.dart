
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'selected_controller.g.dart';

@riverpod
class FlowerShareSelectedPhotosController extends _$FlowerShareSelectedPhotosController {
  @override
  List<PhotoRecord> build() {
    return [];
  }

  void addPhoto(PhotoRecord photo) {
    state = [...state, photo];
  }

  void removePhoto(PhotoRecord photo) {
    state = state.where((p) => p.id != photo.id).toList();
  }

  bool isSelected(PhotoRecord photo) {
    return state.any((p) => p.id == photo.id);
  }

  int? indexOf(PhotoRecord photo) {
    final ret = state.indexWhere((p) => p.id == photo.id);
    if (ret == -1) {
      return null;
    }

    return ret;
  }
}