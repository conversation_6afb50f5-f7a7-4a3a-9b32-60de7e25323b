import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flower_timemachine/pages/flower_share/controller/loader_controller.dart';
import 'package:flower_timemachine/pages/flower_share/controller/selected_controller.dart';
import 'package:flower_timemachine/widgets/error_reporter.dart';
import 'package:flower_timemachine/widgets/media_preview/media_preview.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class FlowerSharePhotoPicker extends ConsumerStatefulWidget {
  const FlowerSharePhotoPicker({
    super.key,
    required this.flower,
    required this.onNext,
  });

  final Flower flower;
  final VoidCallback onNext;

  @override
  ConsumerState<FlowerSharePhotoPicker> createState() => FlowerPhotoPickerState();
}

class FlowerPhotoPickerState extends ConsumerState<FlowerSharePhotoPicker> {
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // 添加滚动监听，实现加载更多
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      final controller = ref.read(flowerShareLoaderControllerProvider(widget.flower).notifier);
      if (controller.hasMore) {
        controller.loadMore();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final photosAsync = ref.watch(flowerShareLoaderControllerProvider(widget.flower));

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        leading: BackButton(onPressed: _onPressBackButton),
        actions: [
          TextButton(onPressed: widget.onNext, child: const Text('flower_share.next').tr())
        ],
      ),
      body: SafeArea(
          child: photosAsync.when(
              error: (error, stackTrace) => ErrorReporter(error: error, stackTrace: stackTrace),
              loading: () => Container(
                  alignment: Alignment.topCenter,
                  child: const SizedBox(height: 100, width: 100, child: CircularProgressIndicator())
              ),
              data: _buildBody
          )
      ),
    );
  }

  Widget _buildBody(List<PhotoRecord> photos) {
    if (photos.isEmpty) {
      return Center(
        child: Text('flower_share.no_photos'.tr()),
      );
    }
    return GridView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(8),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 1.0,
          crossAxisSpacing: 4,
          mainAxisSpacing: 4,
        ),
        itemCount: photos.length,
        itemBuilder: (context, index) => _buildPhotoItem(
            photos[index]
        )
    );
  }

  Widget _buildPhotoItem(PhotoRecord photo) {
    return Consumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
      ref.watch(flowerShareSelectedPhotosControllerProvider);
      return GestureDetector(
        onTap: () => _togglePhotoSelection(photo),
        child: Stack(
          fit: StackFit.expand,
          children: [
            // 显示照片
            MediaPreview(
              path: photo.displayPath,
              mediaType: photo.mediaType,
              width: 64,
            ),
            // 选中状态指示器
            Positioned(
              right: 5,
              top: 5,
              child: _buildSelectionIndicator(photo),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildMediaTypeIndicator(MediaType mediaType) {
    if (mediaType == MediaType.video) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.6),
          shape: BoxShape.circle,
        ),
        padding: const EdgeInsets.all(4),
        child: const Icon(
          Icons.play_arrow,
          color: Colors.white,
          size: 16,
        ),
      );
    } else if (mediaType == MediaType.livePhoto) {
      return Container(
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.6),
          borderRadius: BorderRadius.circular(12),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        child: const Text(
          'LIVE',
          style: TextStyle(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }

  Widget _buildSelectionIndicator(PhotoRecord photo) {
    final selectController = ref.read(flowerShareSelectedPhotosControllerProvider.notifier);
    final index = selectController.indexOf(photo);
    final isSelected = index != null;

    final decoration = BoxDecoration(
      shape: BoxShape.circle,
      color: isSelected ? Theme.of(context).primaryColor : null,
      border: isSelected ? Border.all(color: Colors.white) : null,
    );

    final child = isSelected
        ? Text(
            (index + 1).toString(),
            style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
          )
        : null;

    return GestureDetector(
      onTap: () => _togglePhotoSelection(photo),
      child: Container(
        width: 28,
        height: 28,
        decoration: decoration,
        alignment: Alignment.center,
        child: child,
      ),
    );
  }

  void _togglePhotoSelection(PhotoRecord photo) {
    final controller = ref.read(flowerShareSelectedPhotosControllerProvider.notifier);
    if (controller.isSelected(photo)) {
      controller.removePhoto(photo);
    } else {
      controller.addPhoto(photo);
    }
  }

  void _onPressBackButton() {
    Navigator.pop(context);
  }
}
