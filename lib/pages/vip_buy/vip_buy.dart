import 'dart:async';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/dialog_utils.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/pages/vip_buy/controller/app_purchase_controller.dart';
import 'package:flower_timemachine/pages/vip_buy/widgets/vip_item.dart';
import 'package:flower_timemachine/widgets/ftm_line.dart';
import 'package:flower_timemachine/widgets/loading_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

import 'widgets/vip_buy_button.dart';

class VipBuy extends ConsumerStatefulWidget {
  const VipBuy({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => VipButState();

  static show(BuildContext context) {
    showMaterialModalBottomSheet(
      expand: false,
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => const VipBuy(),
    );
  }
}

class VipButState extends ConsumerState<ConsumerStatefulWidget> {
  late StreamSubscription<List<PurchaseDetails>> _subscription;
  bool restoreModel = false;

  static List<String> vipItems = [
    "vip_page.items.no_limit_tag".tr(),
    "vip_page.items.quick_search".tr(),
    "vip_page.items.cloud_backup".tr(),
    "vip_page.items.custom_nurture".tr(),
    "vip_page.items.auto_nurture_type".tr(),
    "vip_page.items.forever".tr()
  ];

  late final AppPurchaseController controller;

  @override
  Widget build(BuildContext context) {
    final isVip = UserController.get().isVip();
    return Material(
        clipBehavior: Clip.hardEdge,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        child: SafeArea(
            top: false,
            child: Padding(
              padding: const EdgeInsets.only(left: 20, right: 20, top: 20, bottom: 10),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标题
                  Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                    SvgPicture.asset("icons/vip.svg", width: 36, height: 36),
                    const SizedBox(width: 8),
                    const Text(
                      "vip_page.title",
                      style: TextStyle(fontSize: 28),
                    ).tr()
                  ]),
                  const SizedBox(height: 10),
                  const FTMLine(height: 1),
                  const SizedBox(height: 10),
                  // 会员内容介绍
                  for (final item in vipItems) VipItem(text: item),
                  const SizedBox(height: 16),
                  if (!isVip)
                    // 购买和恢复购买按钮
                    Row(children: [
                      TextButton(
                          onPressed: () => restore(ref),
                          child: const Text("vip_page.restore_purchase", style: TextStyle(color: Colors.black54)).tr()),
                      const Spacer(),
                      const VipBuyButton()
                    ])
                  else
                    // 已经是 vip
                    TextButton(
                        onPressed: () {},
                        style: ButtonStyle(
                            backgroundColor: MaterialStateProperty.all(const Color(0xFFFFB000)),
                            foregroundColor: MaterialStateProperty.all(Colors.white),
                            padding:
                                MaterialStateProperty.all(const EdgeInsets.symmetric(vertical: 12, horizontal: 28)),
                            shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12.0),
                            ))),
                        child: const Text("vip_page.already_vip", style: TextStyle(fontSize: 18)).tr())
                ],
              ),
            )));
  }

  void restore(WidgetRef ref) async {
    // 显示 loading 窗口
    LoadingDialog.show();

    restoreModel = true;
    final userId = UserController.get().getID();
    await controller.restore(userId);
  }

  void onData(List<PurchaseDetails> purchaseDetailsList) async {
    // 如果恢复购买并且数据为空，那么是用户没买过 vip
    if (restoreModel && purchaseDetailsList.isEmpty) {
      restoreModel = false;
      safeCloseLoadingDialog();
      showMessageDialog("vip_page.restore_failed_title".tr(), "vip_page.restore_failed_content".tr(), context);
      return;
    }

    for (final detail in purchaseDetailsList) {
      try {
        if (detail.status == PurchaseStatus.purchased || detail.status == PurchaseStatus.restored) {
          // 购买/恢复购买要验证一下
          restoreModel = false;

          bool valid = await _verify(detail);
          safeCloseLoadingDialog();

          if (valid) {
            await UserController.get().setVip(true);
            if (mounted) {
              showMessageDialog("vip_page.purchase_success".tr(), null, context);
            }
          } else {
            final userId = UserController.get().getID();
            if (mounted) {
              showMessageDialog("vip_page.restore_failed_title".tr(),
                  "vip_page.purchase_verify_failed".tr(namedArgs: {"user": userId}), context);
            }
          }
        } else if (detail.status == PurchaseStatus.canceled) {
          restoreModel = false;
          safeCloseLoadingDialog();
          Fluttertoast.showToast(msg: "vip_page.cancel_purchase".tr(), toastLength: Toast.LENGTH_LONG);
        } else if (detail.status == PurchaseStatus.error) {
          debugPrint("系统错误：${detail.error}");
          restoreModel = false;
          safeCloseLoadingDialog();
          if (mounted) {
            showMessageDialog("error".tr(), detail.error!.message, context);
          }
        }
      } catch (e, s) {
        debugPrint("系统错误：$e, $s");
        safeCloseLoadingDialog();
        final sid = await Sentry.captureException(e, stackTrace: s);
        if (mounted) {
          showMessageDialog("system_error".tr(), "exception_code".tr(namedArgs: {"code": sid.toString()}), context);
        }
      }

      if (detail.pendingCompletePurchase) {
        await InAppPurchase.instance.completePurchase(detail);
      }
    }
  }

  void onDone() {
    print("onDone");
    _subscription.cancel();
  }

  void onError(Object error, StackTrace stackTrace) {
    print("onError: $error, $stackTrace}");
  }

  Future<bool> _verify(PurchaseDetails detail) async {
    final userId = UserController.get().getID();
    return await controller.verifyPurchase(detail.purchaseID, detail.verificationData.localVerificationData,
        detail.verificationData.serverVerificationData, userId);
  }

  @override
  void initState() {
    controller = ref.read(appPurchaseControllerProvider);

    final purchaseUpdated = InAppPurchase.instance.purchaseStream;
    _subscription = purchaseUpdated.listen(onData, onDone: onDone, onError: onError);

    super.initState();
  }

  @override
  void dispose() {
    if (Platform.isIOS) {
      final InAppPurchaseStoreKitPlatformAddition iosPlatformAddition =
          InAppPurchase.instance.getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
      iosPlatformAddition.setDelegate(null);
    }
    _subscription.cancel();

    super.dispose();
  }

  void safeCloseLoadingDialog() {
    LoadingDialog.dismiss();
  }
}
