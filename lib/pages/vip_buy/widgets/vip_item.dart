
import 'package:flutter/material.dart';

class VipItem extends StatelessWidget {
  final String text;

  const VipItem({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Icon(Icons.check_circle_outline_outlined, color: Color(0xFFFFB000)),
          Flexible(child: Text(text, style: const TextStyle(fontSize: 18)))
        ]
      )
    );
  }
}