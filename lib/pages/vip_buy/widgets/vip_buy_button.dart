import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/pages/vip_buy/controller/app_purchase_controller.dart';
import 'package:flower_timemachine/widgets/loading_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:sentry_flutter/sentry_flutter.dart';

class VipBuyButton extends ConsumerWidget {
  const VipBuyButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ref.watch(productDetailsProvider).when(
        // 加载商品成功
        data: (details) {
          // 一般商品不会空，提示错误
          if (details.productDetails.isEmpty) {
            return _Button(
              onPressed: () {},
              backgroundColor: Colors.black45,
              child: const Text("vip_page.query_price_failed", style: TextStyle(fontSize: 18)).tr(),
            );
          }

          final detail = details.productDetails[0];

          // 显示价格及描述
          return _Button(
            onPressed: () => buy(detail, ref, context),
            backgroundColor: const Color(0xFFFFB000),
            child: Text("${detail.price}/${detail.title}", style: const TextStyle(fontSize: 18)),
          );
        },
        // 异常
        error: (error, stackTrace) => _Button(
              onPressed: () {},
              backgroundColor: Colors.black45,
              child: FutureBuilder(
                builder: (BuildContext context, AsyncSnapshot<dynamic> snapshot) =>
                    const Text("system_error_and_contact", style: TextStyle(fontSize: 18)).tr(),
                future: Sentry.captureException(error, stackTrace: stackTrace),
              ),
            ),
        // 加载中
        loading: () => _Button(
              onPressed: () {},
              backgroundColor: const Color(0xFFFFB000),
              child: Row(
                children: [
                  const Text("vip_page.loading_price").tr(),
                  const SizedBox(width: 4),
                  const SizedBox(
                    width: 18,
                    height: 18,
                    child: CircularProgressIndicator(color: Colors.white),
                  )
                ],
              ),
            ));
  }

  void buy(ProductDetails detail, WidgetRef ref, BuildContext context) async {
    // 显示 loading 窗口
    LoadingDialog.show();

    final userId = UserController.get().getID();

    try {
      final ret = await ref.read(appPurchaseControllerProvider).buyVip(detail, userId);

      if (!ret) {
        Fluttertoast.showToast(msg: "vip_page.purchase_failed".tr(), toastLength: Toast.LENGTH_LONG);
      }
    } on PlatformException {
      Fluttertoast.showToast(msg: "system_error_and_retry".tr(), toastLength: Toast.LENGTH_LONG);
      // 关闭 loading
      Navigator.pop(context);
    }
  }
}

class _Button extends StatelessWidget {
  final VoidCallback onPressed;
  final Widget child;
  final Color backgroundColor;

  const _Button({required this.onPressed, required this.backgroundColor, required this.child});

  @override
  Widget build(BuildContext context) {
    return TextButton(
        onPressed: onPressed,
        style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all(backgroundColor),
            foregroundColor: MaterialStateProperty.all(Colors.white),
            padding: MaterialStateProperty.all(const EdgeInsets.symmetric(vertical: 12, horizontal: 28)),
            shape: MaterialStateProperty.all<RoundedRectangleBorder>(RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12.0),
            ))),
        child: child);
  }
}
