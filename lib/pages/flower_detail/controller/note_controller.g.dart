// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'note_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$noteControllerHash() => r'1457f5594ac464a680d67fdd3d41df167514646c';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

/// See also [noteController].
@ProviderFor(noteController)
const noteControllerProvider = NoteControllerFamily();

/// See also [noteController].
class NoteControllerFamily extends Family<AsyncValue<String?>> {
  /// See also [noteController].
  const NoteControllerFamily();

  /// See also [noteController].
  NoteControllerProvider call(
    int flowerId,
  ) {
    return NoteControllerProvider(
      flowerId,
    );
  }

  @override
  NoteControllerProvider getProviderOverride(
    covariant NoteControllerProvider provider,
  ) {
    return call(
      provider.flowerId,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'noteControllerProvider';
}

/// See also [noteController].
class NoteControllerProvider extends AutoDisposeFutureProvider<String?> {
  /// See also [noteController].
  NoteControllerProvider(
    int flowerId,
  ) : this._internal(
          (ref) => noteController(
            ref as NoteControllerRef,
            flowerId,
          ),
          from: noteControllerProvider,
          name: r'noteControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$noteControllerHash,
          dependencies: NoteControllerFamily._dependencies,
          allTransitiveDependencies:
              NoteControllerFamily._allTransitiveDependencies,
          flowerId: flowerId,
        );

  NoteControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.flowerId,
  }) : super.internal();

  final int flowerId;

  @override
  Override overrideWith(
    FutureOr<String?> Function(NoteControllerRef provider) create,
  ) {
    return ProviderOverride(
      origin: this,
      override: NoteControllerProvider._internal(
        (ref) => create(ref as NoteControllerRef),
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        flowerId: flowerId,
      ),
    );
  }

  @override
  AutoDisposeFutureProviderElement<String?> createElement() {
    return _NoteControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is NoteControllerProvider && other.flowerId == flowerId;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, flowerId.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin NoteControllerRef on AutoDisposeFutureProviderRef<String?> {
  /// The parameter `flowerId` of this provider.
  int get flowerId;
}

class _NoteControllerProviderElement
    extends AutoDisposeFutureProviderElement<String?> with NoteControllerRef {
  _NoteControllerProviderElement(super.provider);

  @override
  int get flowerId => (origin as NoteControllerProvider).flowerId;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
