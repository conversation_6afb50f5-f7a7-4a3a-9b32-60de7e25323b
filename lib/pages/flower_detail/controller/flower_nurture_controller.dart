import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/flower_monthly_nurture_cycle.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

part 'flower_nurture_controller.g.dart';

@riverpod
Future<List<NurtureType>> flowerNurture(FlowerNurtureRef ref, Flower flower) async {
  // 获取当前月份的养护周期配置
  final currentMonth = DateTime.now().month;
  final List<NurtureType> types = [];

  for (final type in NurtureTypesController.get().enableTypes) {
    final cycle = await FlowerMonthlyNurtureCycle.getEffectiveCycleForMonth(
      flower.id,
      type.id,
      currentMonth
    );

    if (cycle > 0) {
      types.add(type);
    }
  }

  return types;
}
