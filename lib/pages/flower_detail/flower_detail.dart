import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pull_down_button/pull_down_button.dart';

// Controllers
import 'package:flower_timemachine/controller/flower_timeline_controller.dart';
import 'package:flower_timemachine/controller/nurture_list_controller.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'controller/flower_nurture_controller.dart';

// Models
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/models/flower_timeline_editor_item.dart';

// Widgets
import 'package:flower_timemachine/widgets/flower_base_info_widget.dart';
import 'package:flower_timemachine/widgets/ftm_box.dart';
import 'package:flower_timemachine/widgets/loading_dialog.dart';
import 'package:flower_timemachine/widgets/nurture_dialog.dart';

// Pages
import 'package:flower_timemachine/pages/flower_detail/widgets/flower_timeline.dart';
import 'package:flower_timemachine/pages/flower_detail/widgets/note.dart';
import 'package:flower_timemachine/pages/flower_detail/widgets/nurture_records.dart';
import 'package:flower_timemachine/pages/flower_share/flower_share.dart';
import 'package:flower_timemachine/pages/flower_timeline_editor/flower_timeline_editor.dart';
import 'package:flower_timemachine/pages/vip_tips/vip_tips.dart';

// Utils
import 'package:flower_timemachine/common/dialog_utils.dart';
import 'package:flower_timemachine/common/errors.dart';
import 'package:flower_timemachine/common/global.dart';

class FlowerDetailPage extends ConsumerStatefulWidget {
  const FlowerDetailPage({super.key, required this.flower, required this.resultRef});

  static const name = "flower_detail";

  final Flower flower;
  final FlowerDetailResultRef resultRef;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _FlowerDetailPageState();
}

class _FlowerDetailPageState extends ConsumerState<FlowerDetailPage> with SingleTickerProviderStateMixin {
  // Controllers
  late TabController _tabController;
  final ImagePicker picker = ImagePicker();

  // State variables
  final ValueNotifier<bool> editModel = ValueNotifier<bool>(false);
  bool isEdit = false;

  // Constants
  static final dateFormatter = DateFormat.MMMd();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isArchived = widget.flower.archiveTime != null;

    return Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: _buildAppBar(isArchived),
      body: GestureDetector(
          onTap: cancelEditModel,
          child: Container(
              padding: const EdgeInsets.only(left: 10, right: 10),
              child: Column(
                children: [
                  _buildFlowerBaseInfo(isArchived),
                  _buildTabBar(),
                  _buildTabBarView(),
                ],
              ))),
    );
  }

  // 构建AppBar
  AppBar _buildAppBar(bool isArchived) {
    return AppBar(
      elevation: 0,
      actions: [
        if (!isArchived) ...[
          !ShareConfig.getShowOnlySetNurtureTypes()
              ? _FlowerOperatorButton(
                  itemBuilder: addItemButton,
                  child: const Icon(
                    Icons.auto_fix_high,
                    size: 25,
                  ))
              : _FlowerAddOperatorButton(
                  flower: widget.flower,
                  onTap: insertRecord,
                  child: const Icon(Icons.auto_fix_high, size: 25),
                ),
          const SizedBox(width: 15),
          IconButton(icon: SvgPicture.asset('icons/camera.svg', height: 30, width: 30), onPressed: onSendTimeline),
          const SizedBox(width: 15),
          _FlowerOperatorButton(itemBuilder: moreItemButton, child: const Icon(Icons.more_vert, size: 30)),
          const SizedBox(width: 10),
        ],
      ],
    );
  }

  // 构建花的基本信息
  Widget _buildFlowerBaseInfo(bool isArchived) {
    return FlowerBaseInfoWidget(
      flower: widget.flower,
      enableHintIcon: !isArchived,
      enableAutoFixIcon: !isArchived,
      onTap: isArchived ? null : onTapBaseInfo,
      onAutoFix: onAutoFix,
    );
  }

  // 构建TabBar
  Widget _buildTabBar() {
    return FTMBox(
        circular: 5,
        child: TabBar(
          padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 5),
          indicator: BoxDecoration(
            color: const Color(0xFFF6EEAC),
            borderRadius: BorderRadius.circular(5),
          ),
          controller: _tabController,
          tabs: [
            'flower_detail.tab_care'.tr(),
            'flower_detail.tab_timeline'.tr(),
            'flower_detail.tab_note'.tr(),
          ].map((e) => Text(e)).toList(),
          onTap: (i) {
            // 关闭备注页面的键盘
            if (i != 2) {
              FocusManager.instance.primaryFocus?.unfocus();
            }
          },
        ));
  }

  // 构建TabBarView
  Widget _buildTabBarView() {
    return Expanded(
        child: FTMBox(
            circular: 5,
            child: TabBarView(
              controller: _tabController,
              children: [
                NurtureRecordsWidget(flower: widget.flower, editModel: editModel, resultRef: widget.resultRef),
                FlowerTimelineWidget(flower: widget.flower, editModel: editModel),
                FlowerNoteWidget(flowerId: widget.flower.id)
              ],
            )));
  }

  // 添加养护记录按钮菜单项
  List<PullDownMenuEntry> addItemButton(BuildContext context) {
    List<PullDownMenuEntry> buttons = [];
    for (final type in NurtureTypesController.get().enableTypes) {
      buttons.add(
        PullDownMenuItem(
          title: type.name,
          iconWidget: SvgPicture.asset(type.icon),
          onTap: () => insertRecord(type),
        ),
      );
    }

    return buttons;
  }

  // 更多按钮菜单项
  List<PullDownMenuEntry> moreItemButton(BuildContext context) {
    final isArchived = widget.flower.archiveTime != null;
    return [
      PullDownMenuItem(
          title: 'flower_detail.edit_mode'.tr(),
          iconWidget: const Icon(Icons.edit),
          onTap: triggerEditModel,
          itemTheme: const PullDownMenuItemTheme()),
      PullDownMenuItem(
          title: 'flower_detail.share'.tr(),
          iconWidget: const Icon(Icons.ios_share_outlined),
          onTap: onShare,
          itemTheme: const PullDownMenuItemTheme()),
      PullDownMenuItem(
          title: isArchived ? 'restore'.tr() : 'archive'.tr(),
          iconWidget: Icon(isArchived ? Icons.restore_outlined : Icons.archive_outlined),
          onTap: isArchived ? onRestore : onArchive,
          itemTheme: const PullDownMenuItemTheme(textStyle: TextStyle(color: Colors.orange))),
      PullDownMenuItem(
          title: 'delete'.tr(),
          iconWidget: const Icon(Icons.delete),
          onTap: onDelete,
          itemTheme: const PullDownMenuItemTheme(textStyle: TextStyle(color: Colors.red))),
    ];
  }

  void triggerEditModel() {
    editModel.value = !editModel.value;
  }

  void cancelEditModel() {
    if (editModel.value) {
      editModel.value = false;
    }
  }

  void insertRecord(NurtureType type) async {
    final typeText = type.name;

    final result = await NurtureDialog.show(context, typeText);
    if (result == null) {
      return;
    }

    if (DateTime.now().isBefore(result.date)) {
      Fluttertoast.showToast(msg: "flower_detail.record_after_today".tr(), toastLength: Toast.LENGTH_LONG);
      return;
    }

    final date = DateTime.now().copyWith(year: result.date.year, month: result.date.month, day: result.date.day);

    final controller = ref.read(nurtureListControllerControllerProvider(widget.flower).notifier);
    final ret = await controller.insert(type, date, result.remark);
    if (ret == Errors.exist) {
      Fluttertoast.showToast(
          msg: "flower_detail.duplicate_record".tr(namedArgs: {"date": dateFormatter.format(date), "type": typeText}),
          toastLength: Toast.LENGTH_LONG);
      return;
    } else if (ret == Errors.noReady) {
      Fluttertoast.showToast(msg: "system_error_and_retry".tr(), toastLength: Toast.LENGTH_LONG);
      return;
    }

    widget.resultRef.result = FlowerDetailResult.edited;
  }

  Future<void> onAutoFix() async {
    final types = widget.flower.nextTaskRecord?.types;
    if (types == null) {
      Fluttertoast.showToast(msg: "flower_detail.no_need_nurture_record".tr(), toastLength: Toast.LENGTH_LONG);
      return;
    }

    widget.resultRef.result = FlowerDetailResult.edited;

    final controller = ref.read(nurtureListControllerControllerProvider(widget.flower).notifier);
    final insertType = await controller.bulkInsert(types, null);
    if (insertType.length == types.length) {
      // 全部记录插入完成
      return;
    }

    final insertTypeText = insertType.map((e) => e.type.name).join('，');
    Fluttertoast.showToast(
      msg: "flower_detail.auto_nurture_part".tr(namedArgs: {"types": insertTypeText}),
      toastLength: Toast.LENGTH_LONG,
    );
  }

  Future<void> onTapBaseInfo() async {
    final ret = await Navigator.pushNamed(context, 'add_flower', arguments: widget.flower);
    if (ret != null) {
      widget.resultRef.result = FlowerDetailResult.edited;
      ref.invalidate(flowerNurtureProvider(widget.flower));
    }
  }

  Future<void> onDelete() async {
    final name = widget.flower.name;
    final isDel = await showAlertDialog(
      'alert'.tr(),
      'flower_detail.delete_alert'.tr(namedArgs: {"name": name}),
      context,
    );
    if (!(isDel ?? false)) {
      return;
    }

    await widget.flower.delete();

    widget.resultRef.result = FlowerDetailResult.deleted;

    if (mounted) {
      Navigator.pop(context);
    }
  }

  Future<void> onArchive() async {
    if (!UserController.get().isVip()) {
      VipTipsDialog.show("free_user_limit".tr(), context);
      return;
    }

    final result = await Navigator.pushNamed(
      context,
      'archive_info',
      arguments: widget.flower,
    ) as Map<String, dynamic>?;

    if (result == null) {
      return;
    }

    widget.flower.archive(result['timestamp'], reason: result['reason']);

    widget.resultRef.result = FlowerDetailResult.deleted;
    if (mounted) {
      Navigator.pop(context);
    }
  }

  Future<void> onRestore() async {
    await widget.flower.unarchive();

    widget.resultRef.result = FlowerDetailResult.edited;
    setState(() {}); // Refresh UI to update the menu button
  }

  void onSendTimeline() async {
    final result = await Navigator.pushNamed(
      context,
      FlowerTimelineEditor.routeName,
    );
    if (result == null) {
      return;
    }

    // 直接使用返回的 FlowerTimelineEditorItem
    final FlowerTimelineEditorItem item = result as FlowerTimelineEditorItem;

    LoadingDialog.show();
    await FlowerTimelineController.addNewRecordWithMediaItems(widget.flower.id, item, item.mediaItems);
    if (_tabController.index == 0) {
      _tabController.index = 1;
    } else if (_tabController.index == 1) {
      ref.invalidate(flowerTimelineControllerProvider(widget.flower));
    }
    LoadingDialog.dismiss();
  }

  void onShare() async {
    await Navigator.pushNamed(context, FlowerShare.routeName, arguments: widget.flower);
  }
}

class _FlowerOperatorButton extends StatelessWidget {
  final PullDownMenuItemBuilder itemBuilder;
  final Widget child;

  const _FlowerOperatorButton({required this.itemBuilder, required this.child});

  @override
  Widget build(BuildContext context) {
    return PullDownButton(
      itemBuilder: itemBuilder,
      buttonBuilder: (context, showMenu) => GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: showMenu,
        child: child,
      ),
    );
  }
}

class _FlowerAddOperatorButton extends ConsumerWidget {
  final Widget child;
  final Flower flower;
  final void Function(NurtureType) onTap;

  const _FlowerAddOperatorButton({required this.child, required this.flower, required this.onTap});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final nurtureTypes = ref.watch(flowerNurtureProvider(flower));

    final menu = nurtureTypes.when(
      data: (types) => (context) => _dataItemBuilder(context, types),
      error: (_, __) => _errorItemBuilder,
      loading: () => _loadingItemBuilder,
    );

    return _FlowerOperatorButton(
      itemBuilder: menu,
      child: child,
    );
  }

  List<PullDownMenuEntry> _loadingItemBuilder(BuildContext context) {
    return [
      PullDownMenuItem(
        title: 'loading'.tr(),
        enabled: false,
        itemTheme: const PullDownMenuItemTheme(),
        onTap: () {},
      ),
    ];
  }

  List<PullDownMenuEntry> _errorItemBuilder(BuildContext context) {
    return [
      PullDownMenuItem(
        title: 'error'.tr(),
        enabled: false,
        itemTheme: const PullDownMenuItemTheme(),
        onTap: () {},
      ),
    ];
  }

  List<PullDownMenuEntry> _dataItemBuilder(BuildContext context, List<NurtureType> types) {
    if (types.isEmpty) {
      return [
        PullDownMenuItem(
          title: 'no_nurture_type'.tr(),
          enabled: false,
          itemTheme: const PullDownMenuItemTheme(),
          onTap: () {},
        ),
      ];
    }
    return types
        .map((e) => PullDownMenuItem(
              title: e.name,
              iconWidget: SvgPicture.asset(e.icon),
              onTap: () => onTap(e),
            ))
        .toList();
  }
}

enum FlowerDetailResult { nothing, edited, deleted }

class FlowerDetailResultRef {
  FlowerDetailResult? result;
}
