import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/models/flower_notes.dart';
import 'package:flower_timemachine/pages/flower_detail/controller/note_controller.dart';
import 'package:flower_timemachine/pages/vip_tips/vip_tips.dart';
import 'package:flower_timemachine/widgets/error_reporter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class FlowerNoteWidget extends ConsumerStatefulWidget {
  const FlowerNoteWidget({super.key, required this.flowerId});

  final int flowerId;

  @override
  ConsumerState<FlowerNoteWidget> createState() => _FlowerNoteWidgetState();
}

class _FlowerNoteWidgetState extends ConsumerState<FlowerNoteWidget> {
  QuillController? _controller;
  final FocusNode _focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    final isVip = UserController().isVip();

    if (!isVip) {
      _controller = QuillController.basic();
      return GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: _onTap,
        child: Column(
          children: [const Spacer(), _buildToolbar(_controller!)],
        ),
      );
    } else {
      return ref.watch(noteControllerProvider(widget.flowerId)).when(
            data: (data) => _buildEditor(data),
            error: (error, stackTrace) => ErrorReporter(error: error, stackTrace: stackTrace),
            loading: () => const Center(child: SizedBox(height: 20, width: 20, child: CircularProgressIndicator())),
          );
    }
  }

  Widget _buildEditor(String? data) {
    Document document;
    if (data?.isNotEmpty ?? false) {
      document = Document.fromJson(jsonDecode(data!));
    } else {
      document = Document();
    }

    _controller = QuillController(
      document: document,
      configurations: const QuillControllerConfigurations(),
      selection: const TextSelection.collapsed(offset: 0),
    );

    return Padding(
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          Expanded(
            child: QuillEditor.basic(
              focusNode: _focusNode,
              controller: _controller,
              configurations: QuillEditorConfigurations(
                autoFocus: false,
                expands: true,
                onTapOutside: _onTapOutside,
              ),
            ),
          ),
          _buildToolbar(_controller!),
        ],
      ),
    );
  }

  Widget _buildToolbar(QuillController controller) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          IconButton(
            onPressed: _onTapKeyboard,
            icon: const Icon(Icons.keyboard_outlined),
          ),
          QuillSimpleToolbar(
            controller: controller,
            configurations: const QuillSimpleToolbarConfigurations(
              showUndo: false,
              showRedo: false,
              showFontFamily: false,
              showFontSize: false,
              showClipboardCopy: false,
              showClipboardCut: false,
              showClipboardPaste: false,
              showLeftAlignment: false,
              showRightAlignment: false,
              showCenterAlignment: false,
              showJustifyAlignment: false,
              showLink: false,
              showHeaderStyle: false,
              showLineHeightButton: false,
            ),
          )
        ],
      ),
    );
  }

  void _onTap() {
    if (!UserController.get().isVip()) {
      VipTipsDialog.show("free_user_limit".tr(), context);
      return;
    }
  }

  void _onTapOutside(PointerDownEvent event, FocusNode focusNode) async {
    if (_controller == null) {
      return;
    }
    final text = jsonEncode(_controller!.document.toDelta().toJson());
    await FlowerNotes.save(widget.flowerId, text);
  }

  void _onTapKeyboard() async {
    if (_focusNode.hasFocus) {
      _focusNode.unfocus();
    } else {
      _focusNode.requestFocus();
    }

    if (_controller == null) {
      return;
    }
    final text = jsonEncode(_controller!.document.toDelta().toJson());
    await FlowerNotes.save(widget.flowerId, text);
  }

  @override
  void dispose() {
    super.dispose();

    _controller?.dispose();
    _focusNode.dispose();
  }

  @override
  void initState() {
    super.initState();
  }
}
