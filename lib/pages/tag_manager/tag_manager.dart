import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/controller/tag_list_controller.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/pages/vip_tips/vip_tips.dart';
import 'package:flower_timemachine/widgets/error_reporter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:pull_down_button/pull_down_button.dart';
import 'package:showcaseview/showcaseview.dart';

import '../../common/dialog_utils.dart';
import '../../models/tag_info.dart';

class TagManagerPage extends ConsumerStatefulWidget {
  const TagManagerPage({super.key});

  static const routeName = 'tag_manager_page';

  @override
  ConsumerState<TagManagerPage> createState() => _TagManagerPageState();
}

class _TagManagerPageState extends ConsumerState<TagManagerPage> {
  GlobalKey<ShowCaseWidgetState>? showcaseKey;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        centerTitle: true,
        title: const Text('tag_manger').tr(),
        actions: [
          IconButton(
            icon: SvgPicture.asset("icons/add.svg", height: 23, width: 23),
            iconSize: 30,
            onPressed: onAddTag,
            highlightColor: Colors.transparent,
          ),
        ],
      ),
      body: Consumer(
        builder: (context, ref, _) {
          final controller = ref.watch(tagListControllerProvider);
          return controller.when(
            error: (error, stackTrace) => ErrorReporter(error: error, stackTrace: stackTrace),
            loading: () => const Center(child: SizedBox(height: 100, width: 100, child: CircularProgressIndicator())),
            data: (data) => data.isEmpty
                ? Center(
                    child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                        Text('tag_manager_page.first_tag'.tr(), style: const TextStyle(color: Colors.black38))
                      ]))
                : ReorderableListView(
                    onReorder: onReorderTag,
                    children: _buildTagItems(data, context),
                  ),
          );
        },
      ),
    );
  }

  List<Widget> _buildTagItems(Iterable<TagInfo> list, BuildContext context) {
    final result = <Widget>[];
    for (var i = 0; i < list.length; i++) {
      final e = list.elementAt(i);
      Widget widget = ListTile(
        key: ValueKey(e.name),
        title: Text(
          e.name,
          style: const TextStyle(color: Color(0xFF339900)),
        ),
        trailing: PullDownButton(
          itemBuilder: (context) => moreItemButton(context, e),
          buttonBuilder: (context, showMenu) => GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: showMenu,
            child: const Icon(Icons.more_vert),
          ),
        ),
      );

      if (i == 1) {
        showcaseKey = GlobalKey();
        widget = Showcase(
          key: showcaseKey!,
          description: 'long_press_to_reorder'.tr(),
          child: widget,
        );
      }

      result.add(widget);
    }
    return result;
  }

  void onAddTag() async {
    final controller = ref.read(tagListControllerProvider.notifier);
    final isVip = UserController.get().isVip();
    if (!isVip && controller.getNoVipLimit()) {
      VipTipsDialog.show("tag_manager_page.free_user_limit".tr(), context);
      return;
    }

    final newTag = await showTextDialog('tag_manager_page.enter_tag_name'.tr(), 16, context);
    if (newTag == null || newTag.isEmpty) {
      return;
    }

    if (await controller.checkSame(newTag)) {
      Fluttertoast.showToast(msg: "tag_manager_page.same_tag".tr(), toastLength: Toast.LENGTH_SHORT);
      return;
    }

    controller.add(newTag);
  }

  void onRemoveTag(TagInfo tag) async {
    final isDel = await showAlertDialog('tag_manager_page.delete_title'.tr(namedArgs: {"name": tag.name}),
        'tag_manager_page.delete_content'.tr(), context);
    if (isDel == null || isDel == false) {
      return;
    }

    if (ref.read(currentTagProvider) == tag) {
      ref.read(currentTagProvider.notifier).state = allTag;
    }

    ref.read(tagListControllerProvider.notifier).delete(tag);
  }

  void onEditTag(TagInfo tag) async {
    final newTag = await showTextDialog('tag_manager_page.enter_tag_name'.tr(), 16, context);
    if (newTag == null || newTag.isEmpty) {
      return;
    }

    ref.read(tagListControllerProvider.notifier).changeTagName(tag, newTag);
  }

  List<PullDownMenuEntry> moreItemButton(BuildContext context, TagInfo tag) {
    return [
      PullDownMenuItem(
        title: 'editing'.tr(),
        onTap: () => onEditTag(tag),
        icon: Icons.edit,
      ),
      PullDownMenuItem(
        title: 'delete'.tr(),
        onTap: () => onRemoveTag(tag),
        icon: Icons.delete,
        iconColor: Colors.red,
      )
    ];
  }

  void onReorderTag(int oldIndex, int newIndex) {
    ref.read(tagListControllerProvider.notifier).reorderTags(oldIndex, newIndex);
  }

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      await Future.delayed(const Duration(milliseconds: 200));
      final isShow = ShareConfig.getIsShowTagManagerShowCase();
      if (isShow && showcaseKey != null && mounted) {
        ShareConfig.setIsShowTagManagerShowCase(false);
        ShowCaseWidget.of(context).startShowCase([showcaseKey!]);
      }
    });
  }
}
