import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/pages/vip_buy/vip_buy.dart';
import 'package:flutter/cupertino.dart';

class VipTipsDialog extends StatelessWidget {
  final String tips;

  const VipTipsDialog({super.key, required this.tips});

  @override
  Widget build(BuildContext context) {
    return CupertinoAlertDialog(
      title: Text(tips),
      actions: <CupertinoDialogAction>[
        CupertinoDialogAction(
          isDestructiveAction: true,
          onPressed: () => Navigator.pop(context, false),
          child: const Text('vip_page.next_purchase').tr(),
        ),
        CupertinoDialogAction(
          isDefaultAction: true,
          onPressed: () {
            Navigator.pop(context, true);
            VipBuy.show(context);
          },
          child: const Text('vip_page.now_purchase').tr(),
        ),
      ],
    );
  }

  static show(String tips, BuildContext context) async {
    return await showCupertinoModalPopup<bool>(
        context: context, builder: (BuildContext context) => VipTipsDialog(tips: tips));
  }
}
