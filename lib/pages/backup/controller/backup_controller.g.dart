// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'backup_controller.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_$BackupStateNoBackUpImpl _$$BackupStateNoBackUpImplFromJson(
        Map<String, dynamic> json) =>
    _$BackupStateNoBackUpImpl(
      json['downloadDate'] == null
          ? null
          : DateTime.parse(json['downloadDate'] as String),
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$BackupStateNoBackUpImplToJson(
        _$BackupStateNoBackUpImpl instance) =>
    <String, dynamic>{
      'downloadDate': instance.downloadDate?.toIso8601String(),
      'runtimeType': instance.$type,
    };

_$BackupStateUploadedImpl _$$BackupStateUploadedImplFromJson(
        Map<String, dynamic> json) =>
    _$BackupStateUploadedImpl(
      DateTime.parse(json['lastBackupDate'] as String),
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$BackupStateUploadedImplToJson(
        _$BackupStateUploadedImpl instance) =>
    <String, dynamic>{
      'lastBackupDate': instance.lastBackupDate.toIso8601String(),
      'runtimeType': instance.$type,
    };

_$BackupStateUploadingImpl _$$BackupStateUploadingImplFromJson(
        Map<String, dynamic> json) =>
    _$BackupStateUploadingImpl(
      DateTime.parse(json['startDate'] as String),
      (json['progress'] as num).toInt(),
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$BackupStateUploadingImplToJson(
        _$BackupStateUploadingImpl instance) =>
    <String, dynamic>{
      'startDate': instance.startDate.toIso8601String(),
      'progress': instance.progress,
      'runtimeType': instance.$type,
    };

_$BackupStateDownloadedImpl _$$BackupStateDownloadedImplFromJson(
        Map<String, dynamic> json) =>
    _$BackupStateDownloadedImpl(
      DateTime.parse(json['lastDate'] as String),
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$BackupStateDownloadedImplToJson(
        _$BackupStateDownloadedImpl instance) =>
    <String, dynamic>{
      'lastDate': instance.lastDate.toIso8601String(),
      'runtimeType': instance.$type,
    };

_$BackupStateDownloadingImpl _$$BackupStateDownloadingImplFromJson(
        Map<String, dynamic> json) =>
    _$BackupStateDownloadingImpl(
      (json['progress'] as num).toInt(),
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$BackupStateDownloadingImplToJson(
        _$BackupStateDownloadingImpl instance) =>
    <String, dynamic>{
      'progress': instance.progress,
      'runtimeType': instance.$type,
    };

_$BackupStateRestoredImpl _$$BackupStateRestoredImplFromJson(
        Map<String, dynamic> json) =>
    _$BackupStateRestoredImpl(
      DateTime.parse(json['lastDate'] as String),
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$BackupStateRestoredImplToJson(
        _$BackupStateRestoredImpl instance) =>
    <String, dynamic>{
      'lastDate': instance.lastDate.toIso8601String(),
      'runtimeType': instance.$type,
    };

_$BackupStateUnreadyImpl _$$BackupStateUnreadyImplFromJson(
        Map<String, dynamic> json) =>
    _$BackupStateUnreadyImpl(
      $type: json['runtimeType'] as String?,
    );

Map<String, dynamic> _$$BackupStateUnreadyImplToJson(
        _$BackupStateUnreadyImpl instance) =>
    <String, dynamic>{
      'runtimeType': instance.$type,
    };

_$BackupInfoImpl _$$BackupInfoImplFromJson(Map<String, dynamic> json) =>
    _$BackupInfoImpl(
      enableAutoBackup: json['enableAutoBackup'] as bool? ?? false,
      state: json['state'] == null
          ? const BackupState.noBackup(null)
          : BackupState.fromJson(json['state'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$$BackupInfoImplToJson(_$BackupInfoImpl instance) =>
    <String, dynamic>{
      'enableAutoBackup': instance.enableAutoBackup,
      'state': instance.state,
    };

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$backupControllerHash() => r'f9f8b8a524e748f9d45e06cabf47c013b2ff96a7';

/// See also [BackupController].
@ProviderFor(BackupController)
final backupControllerProvider =
    AutoDisposeNotifierProvider<BackupController, BackupInfo>.internal(
  BackupController.new,
  name: r'backupControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$backupControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$BackupController = AutoDisposeNotifier<BackupInfo>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
