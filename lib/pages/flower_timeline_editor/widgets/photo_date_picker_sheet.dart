import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

class PhotoDateInfo {
  final DateTime dateTime;
  final int count;

  PhotoDateInfo({required this.dateTime, required this.count});
}

class PhotoDatePickerSheet extends StatelessWidget {
  final List<PhotoDateInfo> dates;
  final Function(DateTime) onDateSelected;

  const PhotoDatePickerSheet({
    super.key,
    required this.dates,
    required this.onDateSelected,
  });

  @override
  Widget build(BuildContext context) {
    final dateFormat = DateFormat.yMMMd(context.locale.languageCode);
    final timeFormat = DateFormat.Hm(context.locale.languageCode);

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            spreadRadius: 5,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'photo_date_picker.title'.tr(),
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'photo_date_picker.subtitle'.tr(),
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black54,
            ),
          ),
          const SizedBox(height: 16),
          const Divider(height: 1),
          Flexible(
            child: ListView.separated(
              shrinkWrap: true,
              itemCount: dates.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final date = dates[index];
                return ListTile(
                  onTap: () => onDateSelected(date.dateTime),
                  title: Text(
                    dateFormat.format(date.dateTime),
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Text(timeFormat.format(date.dateTime)),
                  trailing: Text(
                    'photo_date_picker.photo_count'.tr(namedArgs: {'count': date.count.toString()}),
                    style: const TextStyle(color: Colors.black54),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('cancel'.tr()),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static Future<DateTime?> show(
    BuildContext context,
    List<PhotoDateInfo> dates,
  ) async {
    if (dates.isEmpty) return null;
    
    return showModalBottomSheet<DateTime>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: PhotoDatePickerSheet(
          dates: dates,
          onDateSelected: (date) => Navigator.pop(context, date),
        ),
      ),
    );
  }
}
