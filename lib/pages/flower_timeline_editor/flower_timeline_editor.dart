import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/flower_timeline_editor_item.dart';
import 'package:flower_timemachine/pages/flower_timeline_editor/controller/flower_timeline_editor_controller.dart';
import 'package:flower_timemachine/pages/flower_timeline_editor/widgets/date_setting_item.dart';
import 'package:flower_timemachine/pages/flower_timeline_editor/widgets/editor_bottom_bar.dart';
import 'package:flower_timemachine/pages/flower_timeline_editor/widgets/grid_view.dart';
import 'package:flower_timemachine/widgets/ftm_line.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class FlowerTimelineEditor extends ConsumerStatefulWidget {
  const FlowerTimelineEditor({super.key, this.timeline});

  static const routeName = 'flower_timeline_editor';

  final FlowerTimelineEditorItem? timeline;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _FlowerTimelineEditorState();
}

class _FlowerTimelineEditorState extends ConsumerState<FlowerTimelineEditor> {
  TextEditingController? textEditingController;
  ValueNotifier<int> currentTextNum = ValueNotifier(0);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          elevation: 0,
          leading: TextButton(onPressed: _onCancel, child: const Text("cancel").tr()),
          leadingWidth: 68,
          actions: [TextButton(onPressed: _onSave, child: const Text("publish").tr())],
        ),
        body: PopScope(
            canPop: false,
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.only(left: 22, right: 22),
                child: Consumer(builder: (BuildContext context, WidgetRef ref, Widget? child) {
                  final provider = flowerTimelineEditorControllerProvider(widget.timeline);
                  final item = ref.watch(provider);
                  final controller = ref.read(provider.notifier);

                  textEditingController ??= TextEditingController(text: item.text);

                  return Column(
                    children: [
                      SizedBox(
                        height: 100,
                        child: TextField(
                          controller: textEditingController,
                          // autofocus: true,
                          keyboardType: TextInputType.multiline,
                          maxLines: null,
                          // inputFormatters: [LengthLimitingTextInputFormatter(FlowerTimelineEditorController.maxText)],
                          decoration: InputDecoration(border: InputBorder.none, hintText: "flower_timeline_editor.text_hint".tr()),
                          onChanged: (val) {
                            currentTextNum.value = val.length;
                          },
                        ),
                      ),
                      FlowerTimelineEditorGridView(controller: controller),
                      const SizedBox(height: 50),
                      const FTMLine(height: 1),
                      FlowerTimelineEditorDateSettingItem(
                        timeline: widget.timeline,
                      ),
                      const FTMLine(height: 1)
                    ],
                  );
                }),
              )
            )
        ),
        bottomSheet: FlowerTimelineEditorBottomBar(currentTextNum: currentTextNum),
        resizeToAvoidBottomInset: true);
  }

  void _onCancel() async {
    Navigator.pop(context);
  }

  void _onSave() async {
    final provider = flowerTimelineEditorControllerProvider(widget.timeline);
    final item = ref.watch(provider);
    final controller = ref.read(provider.notifier);

    final dateTime = item.dateTime;
    if (DateTime.now().isBefore(dateTime)) {
      Fluttertoast.showToast(msg: 'flower_timeline_editor.no_future_time'.tr(), toastLength: Toast.LENGTH_SHORT);
      return;
    }

    final mediaItems = controller.mediaItems;
    final text = textEditingController?.text;

    if (mediaItems.isEmpty && (text?.isEmpty ?? true)) {
      Fluttertoast.showToast(msg: 'flower_timeline_editor.publish_limit'.tr(), toastLength: Toast.LENGTH_SHORT);
      return;
    }

    final ret = FlowerTimelineEditorItem(text: text, mediaItems: mediaItems, dateTime: dateTime);

    // 直接返回编辑项
    Navigator.pop(context, ret);
  }

  @override
  void dispose() {
    super.dispose();
    textEditingController?.dispose();
  }
}

