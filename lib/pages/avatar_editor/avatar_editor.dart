import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:extended_image/extended_image.dart';
import 'package:image_editor/image_editor.dart';

class AvatarEditorPage extends StatefulWidget {
  AvatarEditorPage({required this.imagePath, super.key}) {
    provider = ExtendedFileImageProvider(imagePath);
  }

  final File imagePath;
  late final ImageProvider provider;

  @override
  State<AvatarEditorPage> createState() => _AvatarEditorPageState();
}

class _AvatarEditorPageState extends State<AvatarEditorPage> {
  final GlobalKey<ExtendedImageEditorState> editorKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text('edit_avatar').tr(),
          elevation: 0,
          actions: [TextButton(onPressed: onSave, child: const Text("save").tr())],
        ),
        body: ExtendedImage(
          image: widget.provider,
          extendedImageEditorKey: editorKey,
          mode: ExtendedImageMode.editor,
          fit: BoxFit.contain,
          initEditorConfigHandler: (_) =>
              EditorConfig(
                maxScale: 8.0,
                cropRectPadding: const EdgeInsets.all(1.0),
                hitTestSize: 20.0,
                cropAspectRatio: 1,
              ),
        ));
  }

  void onSave() async {
    final ExtendedImageEditorState? state = editorKey.currentState;
    if (state == null) {
      return;
    }

    final rect = state.getCropRect();
    if (rect == null) {
      return;
    }

    final ImageEditorOption option = ImageEditorOption();
    option.addOption(ClipOption.fromRect(rect));

    final avatar = await ImageEditor.editFileImage(
        file: widget.imagePath, imageEditorOption: option
    );
    if (mounted) {
      Navigator.pop(context, avatar);
    }
  }
}
