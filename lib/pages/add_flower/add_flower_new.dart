import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/dialog_utils.dart';
import 'package:flower_timemachine/pages/add_flower/controller/add_flower_controller.dart';
import 'package:flower_timemachine/pages/advanced_nurture_cycle/advanced_nurture_cycle_page.dart';
import 'package:flower_timemachine/types/monthly_nurture_cycles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../models/flower.dart';
import '../../models/tag_info.dart';
import '../../widgets/ftm_box.dart';
import 'widgets/add_flower_tag_selector.dart';
import 'widgets/alarm_list_widget.dart';
import 'widgets/arrival_time_widget.dart';
import 'widgets/avatar_changeable.dart';

final selectItem1 = Iterable.generate(366, (i) => '$i').toList();

class AddFlowerPage extends ConsumerStatefulWidget {
  const AddFlowerPage({this.flower, super.key});

  final Flower? flower;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AddFlowerPageState();
}

class _AddFlowerPageState extends ConsumerState<AddFlowerPage> {
  final GlobalKey _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  final List<TagInfo> selectedTags = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          centerTitle: true,
          title: Text(widget.flower == null ? "record_new_flower".tr() : "editing".tr()),
          elevation: 0,
          actions: [TextButton(onPressed: onSave, child: const Text("save").tr())],
          leading: TextButton(onPressed: onCancel, child: const Text("cancel").tr()),
          leadingWidth: 68,
        ),
        body: WillPopScope(
            onWillPop: () async { return false; },
            child: SingleChildScrollView(
              child: Container(
                padding: const EdgeInsets.only(left: 10, right: 10, top: 10),
                child: Column(
                  children: [
                    buildBaseInfoWidget(),
                    buildAlarmInfoWidget(),
                    buildOtherOption()
                  ],
                ),
              )
            )
        )
    );
  }

  Widget buildBaseInfoWidget() {
    return FTMBox(
      circular: 10,
      child: Form(
        key: _formKey,
        child: Container(
            padding: const EdgeInsets.all(10),
            child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Consumer(
                    builder: (context, ref, child) {
                      final asyncState = ref.watch(addFlowerControllerProvider(widget.flower));
                      return asyncState.when(
                        data: (state) {
                          final controller = ref.read(addFlowerControllerProvider(widget.flower).notifier);
                          return AvatarChangeableWidget(
                              height: 65,
                              width: 65,
                              avatar: controller.avatar,
                              onChange: (imageData) {
                                controller.setNewAvatar(imageData);
                          });
                        },
                        loading: () => const SizedBox(
                          height: 65,
                          width: 65,
                          child: Center(child: CircularProgressIndicator()),
                        ),
                        error: (error, stack) => const SizedBox(
                          height: 65,
                          width: 65,
                          child: Icon(Icons.error),
                        ),
                      );
                    },
                  ),
                  const SizedBox(width: 10),
                  Flexible(child: TextFormField(
                      controller: _nameController,
                      validator: nameValidator,
                      decoration: InputDecoration(hintText: "flower_name_edit_prompt".tr())
                  )),
                ]
            )
        ),
      ),
    );
  }

  Widget buildAlarmInfoWidget() {
    return FTMBox(
        circular: 10,
        child: Column(
          children: [
            // 标题和高级按钮
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Text(
                    'advanced_nurture_cycle.nurture_reminder'.tr(),
                    style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const Spacer(),
                  GestureDetector(
                    onTap: _onAdvancedTap,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: const Color(0xFFFFB000),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        'advanced_nurture_cycle.advanced'.tr(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // 分割线
            const Divider(height: 1, indent: 16, endIndent: 16),
            // 内容
            Container(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Consumer(
                builder: (context, ref, child) {
                  final asyncState = ref.watch(addFlowerControllerProvider(widget.flower));
                  return asyncState.when(
                    data: (state) => AddFlowerAlarmListWidget(
                      flower: widget.flower,
                    ),
                    loading: () => const Center(child: CircularProgressIndicator()),
                    error: (error, stack) => const Center(child: Icon(Icons.error)),
                  );
                },
              ),
            ),
          ],
        )
    );
  }



  Widget buildOtherOption() {
    return FTMBox(
        circular: 10,
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 5),
          child: Column(
            children: [
              AddFlowerTagSelector(selectedTags: selectedTags),
              Consumer(
                builder: (context, ref, child) {
                  final asyncState = ref.watch(addFlowerControllerProvider(widget.flower));
                  return asyncState.when(
                    data: (state) => ArrivalTimeWidget(
                      flower: widget.flower,
                    ),
                    loading: () => const Center(child: CircularProgressIndicator()),
                    error: (error, stack) => const Center(child: Icon(Icons.error)),
                  );
                },
              )
            ],
          ),
        )
    );
  }

  String? nameValidator(v) {
    if (v!.trim().isEmpty) {
      return "add_flower.flower_name_cannot_empty".tr();
    }
    return null;
  }


  void onSave() async {
    if (!(_formKey.currentState as FormState).validate()) {
      return;
    }

    final asyncState = ref.read(addFlowerControllerProvider(widget.flower));
    if (!asyncState.hasValue) {
      return; // 数据还没加载完成
    }

    final controller = ref.read(addFlowerControllerProvider(widget.flower).notifier);
    controller.selectedTags = selectedTags;
    controller.name = _nameController.text;

    if (!controller.hasChanged) {
      Navigator.pop(context, null);
      return;
    }

    controller.save().then((flower) => Navigator.pop(context, flower));
  }

  void onCancel() async {
    final asyncState = ref.read(addFlowerControllerProvider(widget.flower));
    if (!asyncState.hasValue) {
      Navigator.pop(context, null);
      return;
    }

    final controller = ref.read(addFlowerControllerProvider(widget.flower).notifier);
    controller.selectedTags = selectedTags;
    controller.name = _nameController.text;

    if (controller.hasChanged) {
      final isExit = await showAlertDialog('alert'.tr(), 'unsave_alert'.tr(), context);
      if (isExit == null || !isExit) {
        return;
      }
    }

    if (context.mounted) {
      Navigator.pop(context, null);
    }
  }

  void _onAdvancedTap() async {
    final asyncState = ref.read(addFlowerControllerProvider(widget.flower));
    if (!asyncState.hasValue) {
      return; // 数据还没加载完成
    }

    final controller = ref.read(addFlowerControllerProvider(widget.flower).notifier);
    // 获取当前的月份周期数据
    final monthCycles = controller.loadMonthCyclesForAdvanced();

    if (!mounted) return;

    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AdvancedNurtureCyclePage(
          flowerId: controller.flower?.id ?? -1,
          isNewFlower: controller.flower == null,
          monthCycles: monthCycles,
          nurtureTypes: controller.nurtureTypes,
        ),
      ),
    );

    if (result != null && result is MonthlyNurtureCycles) {
      // 保存临时的月份周期设置
      controller.setTemporaryMonthCycles(result);
    }
  }

  @override
  void initState() {
    super.initState();

    // 初始化文本控制器，如果数据还没加载完成，先使用空字符串
    final asyncState = ref.read(addFlowerControllerProvider(widget.flower));
    final initialName = asyncState.hasValue ? asyncState.value!.name : (widget.flower?.name ?? '');
    final initialTags = asyncState.hasValue ? asyncState.value!.tags : (widget.flower?.tags ?? []);

    _nameController = TextEditingController(text: initialName);
    selectedTags.addAll(initialTags);
  }
}
