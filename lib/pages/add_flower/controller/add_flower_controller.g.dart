// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'add_flower_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$addFlowerControllerHash() =>
    r'cfdba4599a6c0d30584ec0b39a518416fe0d5efd';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$AddFlowerController
    extends BuildlessAutoDisposeAsyncNotifier<AddFlowerState> {
  late final Flower? flower;

  FutureOr<AddFlowerState> build(
    Flower? flower,
  );
}

/// See also [AddFlowerController].
@ProviderFor(AddFlowerController)
const addFlowerControllerProvider = AddFlowerControllerFamily();

/// See also [AddFlowerController].
class AddFlowerControllerFamily extends Family<AsyncValue<AddFlowerState>> {
  /// See also [AddFlowerController].
  const AddFlowerControllerFamily();

  /// See also [AddFlowerController].
  AddFlowerControllerProvider call(
    Flower? flower,
  ) {
    return AddFlowerControllerProvider(
      flower,
    );
  }

  @override
  AddFlowerControllerProvider getProviderOverride(
    covariant AddFlowerControllerProvider provider,
  ) {
    return call(
      provider.flower,
    );
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'addFlowerControllerProvider';
}

/// See also [AddFlowerController].
class AddFlowerControllerProvider extends AutoDisposeAsyncNotifierProviderImpl<
    AddFlowerController, AddFlowerState> {
  /// See also [AddFlowerController].
  AddFlowerControllerProvider(
    Flower? flower,
  ) : this._internal(
          () => AddFlowerController()..flower = flower,
          from: addFlowerControllerProvider,
          name: r'addFlowerControllerProvider',
          debugGetCreateSourceHash:
              const bool.fromEnvironment('dart.vm.product')
                  ? null
                  : _$addFlowerControllerHash,
          dependencies: AddFlowerControllerFamily._dependencies,
          allTransitiveDependencies:
              AddFlowerControllerFamily._allTransitiveDependencies,
          flower: flower,
        );

  AddFlowerControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.flower,
  }) : super.internal();

  final Flower? flower;

  @override
  FutureOr<AddFlowerState> runNotifierBuild(
    covariant AddFlowerController notifier,
  ) {
    return notifier.build(
      flower,
    );
  }

  @override
  Override overrideWith(AddFlowerController Function() create) {
    return ProviderOverride(
      origin: this,
      override: AddFlowerControllerProvider._internal(
        () => create()..flower = flower,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        flower: flower,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<AddFlowerController, AddFlowerState>
      createElement() {
    return _AddFlowerControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is AddFlowerControllerProvider && other.flower == flower;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, flower.hashCode);

    return _SystemHash.finish(hash);
  }
}

mixin AddFlowerControllerRef
    on AutoDisposeAsyncNotifierProviderRef<AddFlowerState> {
  /// The parameter `flower` of this provider.
  Flower? get flower;
}

class _AddFlowerControllerProviderElement
    extends AutoDisposeAsyncNotifierProviderElement<AddFlowerController,
        AddFlowerState> with AddFlowerControllerRef {
  _AddFlowerControllerProviderElement(super.provider);

  @override
  Flower? get flower => (origin as AddFlowerControllerProvider).flower;
}
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
