import 'dart:io';
import 'dart:typed_data';

import 'package:flower_timemachine/common/file_utils.dart';
import 'package:flower_timemachine/common/resources.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/flower_monthly_nurture_cycle.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/models/tag_info.dart';
import 'package:flower_timemachine/types/flower_nurture_cycle.dart';
import 'package:flower_timemachine/types/monthly_nurture_cycles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:collection/collection.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'add_flower_controller.g.dart';
part 'add_flower_controller.freezed.dart';

@freezed
class AddFlowerState with _$AddFlowerState {
  const factory AddFlowerState({
    required bool hasChanged,
    Uint8List? avatar,
    required String name,
    required List<TagInfo> tags,
    required DateTime arrivalTime,
    required MonthlyNurtureCycles monthlyNurtureCycle,
  }) = _AddFlowerState;
}

@riverpod
class AddFlowerController extends _$AddFlowerController {
  Flower? _flower;

  @override
  Future<AddFlowerState> build(Flower? flower) async {
    _flower = flower;

    // 在 build 时就加载月份周期数据
    final monthlyNurtureCycles = await _loadInitialMonthCycles(flower);

    return AddFlowerState(
      hasChanged: false,
      avatar: null,
      name: flower?.name ?? '',
      tags: flower?.tags ?? [],
      arrivalTime: _getInitialArrivalTime(flower),
      monthlyNurtureCycle: monthlyNurtureCycles,
    );
  }

  // 加载初始月份周期数据
  Future<MonthlyNurtureCycles> _loadInitialMonthCycles(Flower? flower) async {
    final result = MonthlyNurtureCycles();

    for (final type in nurtureTypes) {
      Map<int, int> cycles;

      if (flower == null) {
        // 新增花卉，使用空数据
        cycles = <int, int>{};
      } else {
        // 编辑现有花卉，从数据库加载
        cycles = await FlowerMonthlyNurtureCycle.getMonthCycleMap(
          flower.id,
          type.id,
        );
      }

      result.setCyclesForType(type.id, cycles);
    }

    return result;
  }

  DateTime _getInitialArrivalTime(Flower? flower) {
    if (flower?.arrivalTime != null) {
      return DateTime.fromMillisecondsSinceEpoch(flower!.arrivalTime! * 1000);
    }
    if (flower?.createTime != null) {
      return DateTime.fromMillisecondsSinceEpoch(flower!.createTime * 1000);
    }
    return DateTime.now();
  }



  void setNewAvatar(Uint8List image) {
    state = AsyncValue.data(state.requireValue.copyWith(
      hasChanged: true,
      avatar: image,
    ));
  }

  Future<Flower> save() async {
    final currentState = state.requireValue;
    String? avatarFile;
    if (currentState.avatar != null) {
      final littleData = await FlutterImageCompress.compressWithList(currentState.avatar!, minHeight: 256, minWidth: 256);
      avatarFile = await writeToAvatarRecordDir(littleData);
    }

    if (_flower == null) {
      return await _createFlower(avatarFile);
    } else {
      await _updateFlower(avatarFile);
      return _flower!;
    }
  }

  Future<Flower> _createFlower(String? avatarFile) async {
    final currentState = state.requireValue;
    final arrivalTime = (currentState.arrivalTime.millisecondsSinceEpoch / 1000).truncate();

    // 为新花卉创建默认的养护周期
    final List<FlowerNurtureCycle> defaultCycles = [];
    for (final type in nurtureTypes) {
      defaultCycles.add(FlowerNurtureCycle(type, type.defaultCycle));
    }

    final flower = await Flower.create(
        name: currentState.name,
        avatar: avatarFile,
        tags: currentState.tags.isNotEmpty ? currentState.tags : null,
        monthlyNurtureCycles: currentState.monthlyNurtureCycle.isNotEmpty ? currentState.monthlyNurtureCycle : null,
        arrivalTime: arrivalTime);

    return flower;
  }

  Future<void> _updateFlower(String? avatarFile) async {
    final currentState = state.requireValue;
    final arrivalTime = (currentState.arrivalTime.millisecondsSinceEpoch / 1000).truncate();

    // 编辑花卉时，更新基本信息
    _flower!.update(
        name: currentState.name.isNotEmpty ? currentState.name : null,
        avatar: avatarFile,
        tags: currentState.tags.isNotEmpty ? currentState.tags : null,
        monthlyNurtureCycles: currentState.monthlyNurtureCycle.isNotEmpty ? currentState.monthlyNurtureCycle : null,
        arrivalTime: arrivalTime);
  }

  bool get hasChanged => state.hasValue ? state.requireValue.hasChanged : false;

  Image get avatar {
    if (!state.hasValue) {
      return Image.asset(R.iconFlower);
    }
    final currentState = state.requireValue;
    if (currentState.avatar != null) {
      return Image.memory(currentState.avatar!);
    }
    final flowerAvatar = _flower?.avatar;
    if (flowerAvatar != null) {
      return Image.file(File(flowerAvatar));
    } else {
      return Image.asset(R.iconFlower);
    }
  }

  List<TagInfo> get selectedTags => state.hasValue ? state.requireValue.tags : [];

  set selectedTags(List<TagInfo> newTags) {
    if (!state.hasValue) return;

    final currentTags = _flower?.tags ?? [];
    if (const ListEquality().equals(currentTags, newTags)) {
      return;
    }
    state = AsyncValue.data(state.requireValue.copyWith(
      hasChanged: true,
      tags: newTags,
    ));
  }

  String get name => state.hasValue ? state.requireValue.name : '';

  set name(String v) {
    if (!state.hasValue) return;

    if (_flower == null && v.isEmpty) {
      return;
    }
    if (v == _flower?.name) {
      return;
    }

    state = AsyncValue.data(state.requireValue.copyWith(
      hasChanged: true,
      name: v,
    ));
  }



  List<NurtureType> get nurtureTypes => NurtureTypesController.get().enableTypes;

  DateTime get arrivalTime => state.hasValue ? state.requireValue.arrivalTime : DateTime.now();

  set arrivalTime(DateTime time) {
    if (!state.hasValue) return;

    if (state.requireValue.arrivalTime == time) {
      return;
    }

    state = AsyncValue.data(state.requireValue.copyWith(
      hasChanged: true,
      arrivalTime: time,
    ));
  }

  // 获取临时月份周期设置
  MonthlyNurtureCycles get temporaryMonthCycles => state.hasValue ? state.requireValue.monthlyNurtureCycle : MonthlyNurtureCycles();

  // 获取临时月份周期设置（向后兼容）
  Map<int, Map<int, int>> get temporaryMonthCyclesMap => temporaryMonthCycles.toMap();

  // 设置临时月份周期
  void setTemporaryMonthCycles(MonthlyNurtureCycles cycles) {
    if (!state.hasValue) return;

    state = AsyncValue.data(state.requireValue.copyWith(
      hasChanged: true,
      monthlyNurtureCycle: cycles,
    ));
  }

  // 设置临时月份周期（向后兼容）
  void setTemporaryMonthCyclesFromMap(Map<int, Map<int, int>> cycles) {
    if (!state.hasValue) return;

    state = AsyncValue.data(state.requireValue.copyWith(
      hasChanged: true,
      monthlyNurtureCycle: MonthlyNurtureCycles.fromMap(cycles),
    ));
  }

  // 获取当前月份周期数据，用于高级设置页面
  MonthlyNurtureCycles loadMonthCyclesForAdvanced() {
    // 直接返回当前状态中的月份周期数据
    return temporaryMonthCycles.copy();
  }

  // 获取月份周期数据（向后兼容）
  Map<int, Map<int, int>> loadMonthCyclesForAdvancedMap() {
    final result = loadMonthCyclesForAdvanced();
    return result.toMap();
  }

  // 设置指定类型和月份的临时周期
  void setTemporaryCycleForMonth(int typeId, int month, int cycle) {
    if (!state.hasValue) return;

    final newCycles = state.requireValue.monthlyNurtureCycle.copy();
    newCycles.setCycleForMonth(typeId, month, cycle);
    state = AsyncValue.data(state.requireValue.copyWith(
      hasChanged: true,
      monthlyNurtureCycle: newCycles,
    ));
  }

  // 获取指定类型在当前月份的有效周期
  Future<int> getCurrentMonthEffectiveCycle(NurtureType type) async {

    if (!state.hasValue) {
      return type.defaultCycle;
    }

    final currentMonth = DateTime.now().month;
    final currentState = state.requireValue;

    // 先检查临时设置
    final tempCycle = currentState.monthlyNurtureCycle.getCycleForMonth(type.id, currentMonth);
    if (tempCycle != null) {
      return tempCycle;
    }

    // 使用临时数据的有效周期逻辑
    final effectiveCycle = currentState.monthlyNurtureCycle.getEffectiveCycleForMonth(type.id, currentMonth, type);
    if (effectiveCycle != type.defaultCycle) {
      return effectiveCycle;
    }

    // 如果是编辑现有花卉，从数据库获取有效周期
    if (_flower != null) {
      return await FlowerMonthlyNurtureCycle.getEffectiveCycleForMonth(
        _flower!.id,
        type.id,
        currentMonth,
      );
    }

    // 新增花卉，返回默认周期
    return type.defaultCycle;
  }

  // 获取花朵信息（用于向后兼容）
  @override
  Flower? get flower => _flower;
}
