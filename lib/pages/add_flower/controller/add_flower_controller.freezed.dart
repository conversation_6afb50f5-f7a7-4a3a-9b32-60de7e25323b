// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'add_flower_controller.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$AddFlowerState {
  bool get hasChanged => throw _privateConstructorUsedError;
  Uint8List? get avatar => throw _privateConstructorUsedError;
  String get name => throw _privateConstructorUsedError;
  List<TagInfo> get tags => throw _privateConstructorUsedError;
  DateTime get arrivalTime => throw _privateConstructorUsedError;
  MonthlyNurtureCycles get monthlyNurtureCycle =>
      throw _privateConstructorUsedError;

  /// Create a copy of AddFlowerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $AddFlowerStateCopyWith<AddFlowerState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AddFlowerStateCopyWith<$Res> {
  factory $AddFlowerStateCopyWith(
          AddFlowerState value, $Res Function(AddFlowerState) then) =
      _$AddFlowerStateCopyWithImpl<$Res, AddFlowerState>;
  @useResult
  $Res call(
      {bool hasChanged,
      Uint8List? avatar,
      String name,
      List<TagInfo> tags,
      DateTime arrivalTime,
      MonthlyNurtureCycles monthlyNurtureCycle});
}

/// @nodoc
class _$AddFlowerStateCopyWithImpl<$Res, $Val extends AddFlowerState>
    implements $AddFlowerStateCopyWith<$Res> {
  _$AddFlowerStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of AddFlowerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasChanged = null,
    Object? avatar = freezed,
    Object? name = null,
    Object? tags = null,
    Object? arrivalTime = null,
    Object? monthlyNurtureCycle = null,
  }) {
    return _then(_value.copyWith(
      hasChanged: null == hasChanged
          ? _value.hasChanged
          : hasChanged // ignore: cast_nullable_to_non_nullable
              as bool,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as Uint8List?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      tags: null == tags
          ? _value.tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<TagInfo>,
      arrivalTime: null == arrivalTime
          ? _value.arrivalTime
          : arrivalTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      monthlyNurtureCycle: null == monthlyNurtureCycle
          ? _value.monthlyNurtureCycle
          : monthlyNurtureCycle // ignore: cast_nullable_to_non_nullable
              as MonthlyNurtureCycles,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AddFlowerStateImplCopyWith<$Res>
    implements $AddFlowerStateCopyWith<$Res> {
  factory _$$AddFlowerStateImplCopyWith(_$AddFlowerStateImpl value,
          $Res Function(_$AddFlowerStateImpl) then) =
      __$$AddFlowerStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {bool hasChanged,
      Uint8List? avatar,
      String name,
      List<TagInfo> tags,
      DateTime arrivalTime,
      MonthlyNurtureCycles monthlyNurtureCycle});
}

/// @nodoc
class __$$AddFlowerStateImplCopyWithImpl<$Res>
    extends _$AddFlowerStateCopyWithImpl<$Res, _$AddFlowerStateImpl>
    implements _$$AddFlowerStateImplCopyWith<$Res> {
  __$$AddFlowerStateImplCopyWithImpl(
      _$AddFlowerStateImpl _value, $Res Function(_$AddFlowerStateImpl) _then)
      : super(_value, _then);

  /// Create a copy of AddFlowerState
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? hasChanged = null,
    Object? avatar = freezed,
    Object? name = null,
    Object? tags = null,
    Object? arrivalTime = null,
    Object? monthlyNurtureCycle = null,
  }) {
    return _then(_$AddFlowerStateImpl(
      hasChanged: null == hasChanged
          ? _value.hasChanged
          : hasChanged // ignore: cast_nullable_to_non_nullable
              as bool,
      avatar: freezed == avatar
          ? _value.avatar
          : avatar // ignore: cast_nullable_to_non_nullable
              as Uint8List?,
      name: null == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String,
      tags: null == tags
          ? _value._tags
          : tags // ignore: cast_nullable_to_non_nullable
              as List<TagInfo>,
      arrivalTime: null == arrivalTime
          ? _value.arrivalTime
          : arrivalTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
      monthlyNurtureCycle: null == monthlyNurtureCycle
          ? _value.monthlyNurtureCycle
          : monthlyNurtureCycle // ignore: cast_nullable_to_non_nullable
              as MonthlyNurtureCycles,
    ));
  }
}

/// @nodoc

class _$AddFlowerStateImpl implements _AddFlowerState {
  const _$AddFlowerStateImpl(
      {required this.hasChanged,
      this.avatar,
      required this.name,
      required final List<TagInfo> tags,
      required this.arrivalTime,
      required this.monthlyNurtureCycle})
      : _tags = tags;

  @override
  final bool hasChanged;
  @override
  final Uint8List? avatar;
  @override
  final String name;
  final List<TagInfo> _tags;
  @override
  List<TagInfo> get tags {
    if (_tags is EqualUnmodifiableListView) return _tags;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_tags);
  }

  @override
  final DateTime arrivalTime;
  @override
  final MonthlyNurtureCycles monthlyNurtureCycle;

  @override
  String toString() {
    return 'AddFlowerState(hasChanged: $hasChanged, avatar: $avatar, name: $name, tags: $tags, arrivalTime: $arrivalTime, monthlyNurtureCycle: $monthlyNurtureCycle)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AddFlowerStateImpl &&
            (identical(other.hasChanged, hasChanged) ||
                other.hasChanged == hasChanged) &&
            const DeepCollectionEquality().equals(other.avatar, avatar) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality().equals(other._tags, _tags) &&
            (identical(other.arrivalTime, arrivalTime) ||
                other.arrivalTime == arrivalTime) &&
            (identical(other.monthlyNurtureCycle, monthlyNurtureCycle) ||
                other.monthlyNurtureCycle == monthlyNurtureCycle));
  }

  @override
  int get hashCode => Object.hash(
      runtimeType,
      hasChanged,
      const DeepCollectionEquality().hash(avatar),
      name,
      const DeepCollectionEquality().hash(_tags),
      arrivalTime,
      monthlyNurtureCycle);

  /// Create a copy of AddFlowerState
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$AddFlowerStateImplCopyWith<_$AddFlowerStateImpl> get copyWith =>
      __$$AddFlowerStateImplCopyWithImpl<_$AddFlowerStateImpl>(
          this, _$identity);
}

abstract class _AddFlowerState implements AddFlowerState {
  const factory _AddFlowerState(
          {required final bool hasChanged,
          final Uint8List? avatar,
          required final String name,
          required final List<TagInfo> tags,
          required final DateTime arrivalTime,
          required final MonthlyNurtureCycles monthlyNurtureCycle}) =
      _$AddFlowerStateImpl;

  @override
  bool get hasChanged;
  @override
  Uint8List? get avatar;
  @override
  String get name;
  @override
  List<TagInfo> get tags;
  @override
  DateTime get arrivalTime;
  @override
  MonthlyNurtureCycles get monthlyNurtureCycle;

  /// Create a copy of AddFlowerState
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$AddFlowerStateImplCopyWith<_$AddFlowerStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
