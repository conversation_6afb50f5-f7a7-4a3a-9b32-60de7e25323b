import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/pages/add_flower/controller/add_flower_controller.dart';
import 'package:flower_timemachine/widgets/date_picker_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

final _dateFormatter = DateFormat.yMMMd();

class ArrivalTimeWidget extends ConsumerStatefulWidget {
  const ArrivalTimeWidget({super.key, required this.flower});

  final Flower? flower;

  @override
  ConsumerState<ArrivalTimeWidget> createState() => _ArrivalTimeWidgetState();
}

class _ArrivalTimeWidgetState extends ConsumerState<ArrivalTimeWidget> {
  late DateTime arrivalTime;

  @override
  Widget build(BuildContext context) {
    final asyncState = ref.watch(addFlowerControllerProvider(widget.flower));
    return asyncState.when(
      data: (state) {
        arrivalTime = state.arrivalTime;
        return _buildContent();
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => const Center(child: Icon(Icons.error)),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text("add_flower.arrival_time_title".tr(), textAlign: TextAlign.start),
          const SizedBox(height: 5),
          Container(
              padding: const EdgeInsets.all(10),
              constraints: const BoxConstraints(minHeight: 50, minWidth: double.infinity),
              decoration: BoxDecoration(
                  color: const Color(0xfff7f7f7),
                  border: Border.all(width: 1, color: Colors.transparent),
                  borderRadius: BorderRadius.circular(5)),
              child: GestureDetector(
                  onTap: onTapDate,
                  child: Row(
                    children: [
                      Expanded(
                          child: Text(
                            _dateFormatter.format(arrivalTime),
                            style: const TextStyle(fontSize: 18),
                          )),
                      const SizedBox(width: 5),
                      const Icon(Icons.arrow_forward_ios, color: Colors.grey)
                    ],
                  )))
        ],
      ),
    );
  }

  void onTapDate() async {
    if (context.mounted) {
      final newDate = await FTMDatePickerDialog.show(context, "confirm".tr(),
          defaultDate: arrivalTime, mode: CupertinoDatePickerMode.date, maximumDate: DateTime.now());

      if (newDate == null) {
        return;
      }

      setState(() {
        final setDate = arrivalTime.copyWith(year: newDate.year, month: newDate.month, day: newDate.day);
        if (DateTime.now().isBefore(setDate)) {
          arrivalTime = DateTime.now();
        } else {
          arrivalTime = arrivalTime.copyWith(year: newDate.year, month: newDate.month, day: newDate.day);
        }

        ref.read(addFlowerControllerProvider(widget.flower).notifier).arrivalTime = arrivalTime;
      });
    }
  }

  @override
  void initState() {
    super.initState();
    // 初始化时间，如果数据还没加载完成，使用默认时间
    final asyncState = ref.read(addFlowerControllerProvider(widget.flower));
    if (asyncState.hasValue) {
      arrivalTime = asyncState.value!.arrivalTime;
    } else {
      arrivalTime = widget.flower?.arrivalTime != null
          ? DateTime.fromMillisecondsSinceEpoch(widget.flower!.arrivalTime! * 1000)
          : DateTime.now();
    }
  }
}
