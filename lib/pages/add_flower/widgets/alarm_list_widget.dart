import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flutter/material.dart';

import 'alarm_widget.dart';

final selectItem1 = Iterable.generate(366, (i) => '$i').toList();

class AddFlowerAlarmListWidget extends StatelessWidget {
  const AddFlowerAlarmListWidget({super.key, required this.flower});

  final Flower? flower;

  @override
  Widget build(BuildContext context) {
    final types = NurtureTypesController.get().enableTypes;
    return ListView.separated(
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        itemBuilder: (context, index) {
          return AddFlowerAlarmWidget(
            type: types[index],
            pickerData: selectItem1,
            flower: flower,
          );
        },
        separatorBuilder: (context, index) {
          return const Divider(indent: 10, endIndent: 10);
        },
        itemCount: types.length
    );
  }

}