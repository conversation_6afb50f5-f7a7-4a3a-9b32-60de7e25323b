import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/pages/add_flower/controller/add_flower_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AddFlowerAlarmWidget extends ConsumerStatefulWidget {
  const AddFlowerAlarmWidget({
    super.key,
    required this.type,
    required this.pickerData,
    required this.flower,
  });

  final NurtureType type;
  final List<String> pickerData;
  final Flower? flower;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AddFlowerAlarmWidgetState();
}

class _AddFlowerAlarmWidgetState extends ConsumerState<AddFlowerAlarmWidget>{
  @override
  Widget build(BuildContext context) {
    final asyncState = ref.watch(addFlowerControllerProvider(widget.flower));
    return asyncState.when(
      data: (state) {
        return FutureBuilder<int>(
          future: ref.read(addFlowerControllerProvider(widget.flower).notifier).getCurrentMonthEffectiveCycle(widget.type),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return buildBody(const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ));
            }

            final cycle = snapshot.data ?? widget.type.defaultCycle;
            Widget cycleText;
            if (cycle == 0) {
              cycleText = Text("不进行养护", style: const TextStyle(color: Colors.black54));
            } else {
              cycleText = Text("${cycle}天", style: const TextStyle(color: Colors.black));
            }

            return GestureDetector(
              behavior: HitTestBehavior.opaque,
              child: buildBody(cycleText),
              onTap: () => _showCycleDialog(context),
            );
          },
        );
      },
      loading: () => buildBody(const SizedBox(
        width: 16,
        height: 16,
        child: CircularProgressIndicator(strokeWidth: 2),
      )),
      error: (error, stack) => buildBody(const Icon(Icons.error)),
    );
  }

  Widget buildBody(Widget cycle) {
    return Container(
        padding: const EdgeInsets.all(5),
        child: Row(children: [
          Container(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: SvgPicture.asset(widget.type.icon, width: 16, height: 16),
          ),
          Text(widget.type.name),
          const Spacer(),
          cycle,
          const SizedBox(width: 5),
          const Icon(Icons.edit)
        ])
    );
  }

  void _showCycleDialog(BuildContext context) async {
    final controller = ref.read(addFlowerControllerProvider(widget.flower).notifier);
    int selectedCycle = await controller.getCurrentMonthEffectiveCycle(widget.type);

    if (!context.mounted) return;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // 标题
                    Row(
                      children: [
                        SvgPicture.asset(widget.type.icon, width: 20, height: 20),
                        const SizedBox(width: 8),
                        Text(
                          widget.type.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(Icons.close, size: 20),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),

                    // 子标题
                    Text(
                      'advanced_nurture_cycle.select_cycle_days'.tr(),
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // 滚轮选择器
                    Container(
                      height: 200,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: CupertinoPicker(
                        itemExtent: 40,
                        scrollController: FixedExtentScrollController(initialItem: selectedCycle),
                        onSelectedItemChanged: (index) {
                          setDialogState(() {
                            selectedCycle = index;
                          });
                        },
                        children: widget.pickerData.map((item) =>
                          Center(
                            child: Text(
                              '$item天',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ).toList(),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // 当前选中的值显示
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      decoration: BoxDecoration(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        'advanced_nurture_cycle.current_selection'.tr(namedArgs: {'cycle': widget.pickerData[selectedCycle]}),
                        style: TextStyle(
                          color: Theme.of(context).primaryColor,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // 按钮区域
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              _setCycleForCurrentMonth(selectedCycle);
                              Navigator.of(context).pop();
                            },
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              backgroundColor: Colors.grey.shade100,
                              foregroundColor: Colors.black87,
                            ),
                            child: Text('advanced_nurture_cycle.current_month_only'.tr()),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              _setCycleForAllMonths(selectedCycle);
                              Navigator.of(context).pop();
                            },
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text('advanced_nurture_cycle.apply_all_months'.tr()),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  // 仅为当前月份设置周期
  void _setCycleForCurrentMonth(int cycle) {
    final controller = ref.read(addFlowerControllerProvider(widget.flower).notifier);
    final currentMonth = DateTime.now().month;
    controller.setTemporaryCycleForMonth(widget.type.id, currentMonth, cycle);
  }

  // 为所有月份设置周期
  void _setCycleForAllMonths(int cycle) {
    final controller = ref.read(addFlowerControllerProvider(widget.flower).notifier);
    for (int month = 1; month <= 12; month++) {
      controller.setTemporaryCycleForMonth(widget.type.id, month, cycle);
    }
  }
}