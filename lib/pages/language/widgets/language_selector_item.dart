
import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/pages/language/controller/language_controller.dart';
import 'package:flower_timemachine/widgets/loading_dialog.dart';
import 'package:flutter/material.dart';

class LanguageSelectorItem extends StatefulWidget {
  final Locale locale;

  const LanguageSelectorItem({super.key, required this.locale});

  @override
  State<LanguageSelectorItem> createState() => _LanguageSelectorItemState();
}

class _LanguageSelectorItemState extends State<LanguageSelectorItem> {
  @override
  Widget build(BuildContext context) {
    final isCheck = context.locale == widget.locale;
    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 8),
        child: Row(
          children: [
            if (isCheck)
              const Icon(Icons.check, size: 24)
            else
              const SizedBox(width: 24),
            const SizedBox(width: 8),
            Text(
              LanguageController.languageCodeMapping(widget.locale)!,
              style: const TextStyle(fontSize: 20),
            )
          ],
        ),
      ),
    );
  }

  void onTap() async {
    if (context.locale == widget.locale) {
      return;
    }

    LoadingDialog.show();

    // 调用这个函数会刷新整个 APP
    await context.setLocale(widget.locale);

    final engine = WidgetsFlutterBinding.ensureInitialized();
    await engine.performReassemble();

    LoadingDialog.dismiss();
  }
}