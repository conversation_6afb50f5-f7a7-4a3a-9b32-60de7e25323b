
import 'dart:ui';

class LanguageController {
  static String? languageCodeMapping(Locale locale) {
    if (locale.languageCode == "zh") {
      final code = locale.scriptCode;
      if (code == null || code == "Hans") {
        return "简体中文";
      } else {
        return "繁体中文";
      }
    }

    return _languageCodeMapping[locale.languageCode];
  }

  static List<Locale> supportedLocales() => _supportedLocales;
}

const _languageCodeMapping = {
  "en": "English",
  "ja": "日本語",
  "ko": "한국어",
};

const _supportedLocales = [
  Locale('en'),
  Locale.fromSubtags(languageCode: "zh", scriptCode: "Hans"),
  Locale.fromSubtags(languageCode: "zh", scriptCode: "Hant"),
  Locale("ja"),
  Locale("ko"),
];