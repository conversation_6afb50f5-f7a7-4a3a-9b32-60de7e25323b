import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/album_export_controller.dart';
import 'package:flower_timemachine/controller/flower_list_controller.dart';
import 'package:flower_timemachine/controller/flower_pending_auto_nurture_controller.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/pages/vip_tips/vip_tips.dart';
import 'package:flower_timemachine/widgets/loading_dialog.dart';
import 'package:flower_timemachine/widgets/nurture_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:pull_down_button/pull_down_button.dart';

import '../../../controller/garden_controller.dart';

class HomeBottomBar extends HookConsumerWidget {
  final Widget child;

  const HomeBottomBar({required this.child, super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final appNavigationState = ref.watch(appNavigationStateProvider);

    // 如果相等，代表是页面切换或 App 刚启动，不使用动画
    final lastAppNavigationStateValue = useRef(appNavigationState);
    if (appNavigationState == lastAppNavigationStateValue.value) {
      return child;
    } else {
      lastAppNavigationStateValue.value = appNavigationState;
    }

    // 进入/退出养护模式，清除待处理的列表
    ref.read(flowerPendingAutoNurtureControllerProvider).clear();
    ref.read(albumExportControllerProvider).clear();

    final animationController = useAnimationController(
      duration: const Duration(milliseconds: 300),
    );
    animationController.reset();
    animationController.forward();

    return _HomeBarAnimation(
      appNavigationState: appNavigationState,
      controller: animationController,
      ref: ref,
      child: child,
    );
  }
}

class _HomeBarAnimation extends AnimatedWidget {
  final AppNavigationState appNavigationState;
  final Widget child;
  final WidgetRef ref;

  const _HomeBarAnimation(
      {required this.child,
      required this.appNavigationState,
      required AnimationController controller,
      required this.ref})
      : super(listenable: controller);

  @override
  Widget build(BuildContext context) {
    Animation<double> animation = listenable as Animation<double>;

    if (appNavigationState == AppNavigationState.home) {
      // 如果是 app 刚启动，不显示 home bar
      return SizeTransition(
        sizeFactor: animation,
        child: child,
      );
    }

    Widget bottomBar;
    if (appNavigationState == AppNavigationState.autoNurture) {
      bottomBar = const _AutoNurtureBar();
    } else {
      bottomBar = const _ExportAlbumBar();
    }

    return SizeTransition(
        sizeFactor: animation,
        child: Container(
          height: 72,
          padding: const EdgeInsets.symmetric(horizontal: 8),
          child: bottomBar,
        ));
  }
}

class _AutoNurtureBar extends ConsumerWidget {
  const _AutoNurtureBar();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        TextButton(
            onPressed: () {
              ref.read(appNavigationStateProvider.notifier).state = AppNavigationState.home;
            },
            child: const Text('cancel').tr()),
        TextButton(onPressed: () => onTapCheckAll(ref), child: const Text('check_all').tr()),
        const Spacer(),
        PullDownButton(
          itemBuilder: (context) => bulkNurtureItemBuilder(context, ref),
          buttonBuilder: (context, showMenu) => GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: showMenu,
            child: const Text("home_page.batch_care").tr(),
          ),
        ),
        const SizedBox(width: 8),
        TextButton(onPressed: () => onTapAutoNurture(ref), child: const Text('home_page.auto_care').tr()),
      ],
    );
  }

  List<PullDownMenuEntry> bulkNurtureItemBuilder(BuildContext context, WidgetRef ref) {
    List<PullDownMenuEntry> buttons = [];
    for (final type in NurtureTypesController.get().enableTypes) {
      buttons.add(PullDownMenuItem(
        title: type.name,
        iconWidget: SvgPicture.asset(type.icon),
        onTap: () => onTapBulkNurture(ref, type, context),
      ));
    }

    return buttons;
  }

  void onTapAutoNurture(WidgetRef ref) async {
    final pendingListController = ref.read(flowerPendingAutoNurtureControllerProvider.notifier);
    if (pendingListController.needAutoNurtureFlower()) {
      final controller = ref.read(flowerListControllerProvider);
      await controller.fireAutoNurture(pendingListController.get());
      pendingListController.clear();
      ref.invalidate(flowerListControllerProvider);
    }

    ref.read(appNavigationStateProvider.notifier).state = AppNavigationState.home;
  }

  void onTapBulkNurture(WidgetRef ref, NurtureType type, BuildContext context) async {
    if (!UserController.get().isVip()) {
      VipTipsDialog.show("free_user_limit".tr(), context);
      return;
    }

    final result = await NurtureDialog.show(context, type.name);
    if (result == null) {
      return;
    }

    if (DateTime.now().isBefore(result.date)) {
      Fluttertoast.showToast(
        msg: "flower_detail.record_after_today".tr(),
        toastLength: Toast.LENGTH_LONG,
      );
      return;
    }

    final date = DateTime.now().copyWith(year: result.date.year, month: result.date.month, day: result.date.day);

    final pendingListController = ref.read(flowerPendingAutoNurtureControllerProvider.notifier);
    if (pendingListController.needAutoNurtureFlower()) {
      final controller = ref.read(flowerListControllerProvider);
      await controller.fireBulkNurture(type, pendingListController.get(), date, result.remark);

      pendingListController.clear();
      ref.invalidate(flowerListControllerProvider);
    }

    ref.read(appNavigationStateProvider.notifier).state = AppNavigationState.home;
  }

  void onTapCheckAll(WidgetRef ref) {
    final pendingListController = ref.read(flowerPendingAutoNurtureControllerProvider.notifier);
    final controller = ref.read(flowerListControllerProvider);

    pendingListController.checkAll(controller.data);
  }
}

class _ExportAlbumBar extends ConsumerWidget {
  const _ExportAlbumBar();

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        TextButton(onPressed: () => _onTapCancel(ref), child: const Text('cancel').tr()),
        const Spacer(),
        TextButton(onPressed: () => _onTapExport(ref), child: const Text('export').tr()),
      ],
    );
  }

  void _onTapExport(WidgetRef ref) async {
    final selectedPhotos = ref.read(albumExportControllerProvider);

    LoadingDialog.show(text: "saveing".tr());

    for (final photo in selectedPhotos) {
      await ImageGallerySaver.saveFile(photo.file);
    }

    LoadingDialog.dismiss();

    Fluttertoast.showToast(msg: "show_photo_page.save_success".tr(), toastLength: Toast.LENGTH_LONG);

    ref.read(appNavigationStateProvider.notifier).state = AppNavigationState.home;
  }

  void _onTapCancel(WidgetRef ref) {
    ref.read(appNavigationStateProvider.notifier).state = AppNavigationState.home;
  }
}
