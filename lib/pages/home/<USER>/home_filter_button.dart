import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/controller/current_home_view_mode.dart';
import 'package:flower_timemachine/controller/flower_list_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/svg.dart';
import 'package:popover/popover.dart';

import 'package:flower_timemachine/types/flower_list_sort_type.dart';

class HomeFilterButton extends ConsumerWidget {
  const HomeFilterButton({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentType = ref.watch(currentFlowerListSortTypeProvider);
    final currentViewMode = ref.watch(currentHomeViewModeProvider);

    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        showPopover(
          context: context,
          bodyBuilder: (context) => _FilterMenu(
            currentType: currentType,
            currentViewMode: currentViewMode,
            ref: ref,
          ),
          direction: PopoverDirection.bottom,
          backgroundColor: Colors.white,
          arrowHeight: 10,
          arrowWidth: 15,
        );
      },
      child: SvgPicture.asset('icons/filter.svg', width: 25, height: 25),
    );
  }
}

class _FilterMenu extends StatelessWidget {
  final FlowerListSortType currentType;
  final HomeViewMode currentViewMode;
  final WidgetRef ref;

  const _FilterMenu({
    required this.currentType,
    required this.currentViewMode,
    required this.ref,
  });

  // 排序选项配置
  static final Map<FlowerListSortType, String> _sortOptions = {
    FlowerListSortType.nextMaintenanceAsc: 'home_page.sort_care_asc',
    FlowerListSortType.nextMaintenanceDesc: 'home_page.sort_care_desc',
    FlowerListSortType.companionTimeAsc: 'home_page.sort_companion_asc',
    FlowerListSortType.companionTimeDesc: 'home_page.sort_companion_desc',
    FlowerListSortType.nameAsc: 'home_page.sort_name_asc',
    FlowerListSortType.nameDesc: 'home_page.sort_name_desc',
  };

  // 视图模式配置
  static final Map<HomeViewMode, String> _viewModeOptions = {
    HomeViewMode.list: 'home_page.list_view',
    HomeViewMode.grid: 'home_page.grid_view',
  };

  // 处理排序选项点击
  void _handleSortOptionTap(BuildContext context, FlowerListSortType type) {
    if (currentType != type) {
      ref.read(currentFlowerListSortTypeProvider.notifier).state = type;
      ShareConfig.setFlowerListSortType(type);
    }
    Navigator.of(context).pop();
  }

  // 处理视图模式切换
  void _handleViewModeTap(BuildContext context, HomeViewMode mode) {
    if (currentViewMode != mode) {
      ShareConfig.setHomeViewMode(mode);
      ref.read(currentHomeViewModeProvider.notifier).state = mode;
    }
    Navigator.of(context).pop();
  }

  // 创建排序选项菜单项
  List<Widget> _buildSortMenuItems(BuildContext context) {
    return _sortOptions.entries.map((entry) {
      return _MenuItem(
        title: entry.value.tr(),
        icon: currentType == entry.key ? Icons.check : null,
        onTap: () => _handleSortOptionTap(context, entry.key),
      );
    }).toList();
  }

  // 创建视图模式菜单项
  List<Widget> _buildViewModeItems(BuildContext context) {
    return _viewModeOptions.entries.map((entry) {
      return _MenuItem(
        title: entry.value.tr(),
        icon: currentViewMode == entry.key ? Icons.check : null,
        onTap: () => _handleViewModeTap(context, entry.key),
      );
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: IntrinsicWidth(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            ..._buildSortMenuItems(context),
            const Divider(height: 1),
            ..._buildViewModeItems(context),
          ],
        ),
      ),
    );
  }
}

class _MenuItem extends StatelessWidget {
  final String title;
  final IconData? icon;
  final VoidCallback onTap;

  const _MenuItem({
    required this.title,
    this.icon,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 18,
              child: icon != null ? Icon(icon, size: 18) : null,
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(title),
            ),
          ],
        ),
      ),
    );
  }
}
