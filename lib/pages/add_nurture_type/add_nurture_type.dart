import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/common/dialog_utils.dart';
import 'package:flower_timemachine/controller/user_controller.dart';
import 'package:flower_timemachine/pages/vip_tips/vip_tips.dart';
import 'package:flutter_picker_plus/flutter_picker_plus.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'controller/add_nurture_type_controller.dart';

final pickerData = Iterable.generate(366, (i) => '$i').toList();

class AddNurtureType extends ConsumerStatefulWidget {
  static const routeName = "add_nurture_type";

  const AddNurtureType({super.key, this.type});

  final NurtureType? type;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AddNurtureTypeState();
}

class _AddNurtureTypeState extends ConsumerState<AddNurtureType> {
  late TextEditingController _nameController;
  final GlobalKey _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final controller = ref.watch(addNurtureTypeControllerProvider(widget.type));

    final picker = Picker(
        title: Text("add_nurture.default_care_cycle", style: TextStyle(color: Theme.of(context).primaryColor)).tr(),
        adapter: PickerDataAdapter<String>(pickerData: pickerData),
        cancelText: 'cancel'.tr(),
        confirmText: 'confirm'.tr(),
        onConfirm: onConfirm
    );

    return Scaffold(
        appBar: AppBar(
          elevation: 0,
          title: Text(controller.title),
          centerTitle: true,
          actions: [TextButton(onPressed: onSave, child: const Text("save").tr())],
          leading: TextButton(onPressed: onCancel, child: const Text("cancel").tr()),
          leadingWidth: 68,
        ),
        body: SingleChildScrollView(
          child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        alignment: Alignment.center,
                        width: 64,
                        height: 64,
                        decoration: BoxDecoration(
                            border: Border.all(color: Theme.of(context).primaryColor),
                            borderRadius: const BorderRadius.all(Radius.circular(8))
                        ),
                        child: SvgPicture.asset(controller.icon, width: 32, height: 32),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                          child: Form(
                              key: _formKey,
                              child: TextFormField(
                                  controller: _nameController,
                                  validator: nameValidator,
                                  decoration: InputDecoration(
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        borderSide: BorderSide(
                                          color: Theme.of(context).primaryColor,
                                        ),
                                      ),
                                      enabledBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        borderSide: BorderSide(
                                          color: Theme.of(context).primaryColor,
                                        ),
                                      ),
                                      focusedErrorBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        borderSide: const BorderSide(
                                          color: Colors.red,
                                        ),
                                      ),
                                      errorBorder: OutlineInputBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        borderSide: const BorderSide(
                                          color: Colors.red,
                                        ),
                                      ),
                                      hintText: 'add_nurture.nurture_name_prompt'.tr()
                                  )
                              )
                          )
                      ),
                      const SizedBox(width: 8),
                      GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () => picker.showModal(context),
                          child: Container(
                            alignment: Alignment.center,
                            width: 64,
                            height: 64,
                            decoration: BoxDecoration(
                                border: Border.all(color: Theme.of(context).primaryColor),
                                borderRadius: const BorderRadius.all(Radius.circular(8))
                            ),
                            child: Text(controller.cycle.toString()),
                          )
                      )
                    ],
                  ),
                  const Padding(
                    padding: EdgeInsets.symmetric(vertical: 8),
                    child: Divider(thickness: 1),
                  ),
                  GridView.count(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    crossAxisCount: 4,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    children: _buildIcons().toList(),
                  )
                ],
              )
          ),
        )
    );
  }

  Iterable<Widget> _buildIcons() sync* {
    final controller = ref.read(addNurtureTypeControllerProvider(widget.type));
    for (final item in controller.nurtureTypeIcons) {
      Color color;
      if (item == controller.icon) {
        color = Theme.of(context).primaryColor;
      } else {
        color = Colors.black38;
      }

      yield GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            controller.icon = item;
          },
          child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                  border: Border.all(color: color),
                  borderRadius: const BorderRadius.all(Radius.circular(8))
              ),
              child: SvgPicture.asset(item, color: color)
          )
      );
    }
  }

  String? nameValidator(v) {
    if (v!.trim().isEmpty) {
      return "add_nurture.nurture_name_empty".tr();
    } else if (v.length > 16) {
      return "add_nurture.nurture_name_length_limit".tr();
    }
    return null;
  }

  void onConfirm(Picker picker, List<int> selected) {
    final controller = ref.read(addNurtureTypeControllerProvider(widget.type));
    controller.cycle = selected[0];
  }

  void onSave() async {
    if (!UserController.get().isVip()) {
      VipTipsDialog.show("free_user_limit".tr(), context);
      return;
    }

    if (!(_formKey.currentState as FormState).validate()) {
      return;
    }

    final controller = ref.read(addNurtureTypeControllerProvider(widget.type));

    controller.name = _nameController.text;

    if (!controller.isChange) {
      Navigator.pop(context, null);
      return;
    }
    
    final nurType = await controller.save();
    if (mounted) {
      Navigator.pop(context, nurType);
    }
  }

  void onCancel() async {
    final controller = ref.read(addNurtureTypeControllerProvider(widget.type));
    controller.name = _nameController.text;

    if (controller.isChange) {
      final isExit = await showAlertDialog('alert'.tr(), 'unsave_alert'.tr(), context);
      if (isExit == null || !isExit) {
        return;
      }
    }

    if (context.mounted) {
      Navigator.pop(context, null);
    }
  }

  @override
  void initState() {
    super.initState();

    final controller = ref.read(addNurtureTypeControllerProvider(widget.type));
    _nameController = TextEditingController(text: controller.name);
  }
}