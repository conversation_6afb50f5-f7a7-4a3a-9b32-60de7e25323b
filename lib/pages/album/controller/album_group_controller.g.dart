// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'album_group_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$albumGroupControllerHash() =>
    r'4624343d835ecfa342680e97f3a4a0a5c44cd25c';

/// See also [AlbumGroupController].
@ProviderFor(AlbumGroupController)
final albumGroupControllerProvider = AutoDisposeNotifierProvider<
    AlbumGroupController, List<AlbumGroup>>.internal(
  AlbumGroupController.new,
  name: r'albumGroupControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$albumGroupControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AlbumGroupController = AutoDisposeNotifier<List<AlbumGroup>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
