// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'album_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$albumControllerHash() => r'32ced2a68a2a083cedc58cc0e2983bf537f3754c';

/// See also [AlbumController].
@ProviderFor(AlbumController)
final albumControllerProvider = AutoDisposeAsyncNotifierProvider<
    AlbumController, List<PhotoRecord>>.internal(
  AlbumController.new,
  name: r'albumControllerProvider',
  debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
      ? null
      : _$albumControllerHash,
  dependencies: null,
  allTransitiveDependencies: null,
);

typedef _$AlbumController = AutoDisposeAsyncNotifier<List<PhotoRecord>>;
// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member
