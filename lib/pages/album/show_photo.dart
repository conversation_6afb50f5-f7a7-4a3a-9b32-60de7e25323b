import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:photo_view/photo_view.dart';
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flower_timemachine/widgets/media_renderer.dart';

import 'controller/album_controller.dart';

class AlbumShowPhotoPage extends ConsumerStatefulWidget {
  const AlbumShowPhotoPage({
    super.key,
    required this.currentPhoto,
  });

  final PhotoRecord currentPhoto;

  static const routeName = 'album_show_photo';

  @override
  ConsumerState<AlbumShowPhotoPage> createState() => _AlbumShowPhotoPageState();
}

class _AlbumShowPhotoPageState extends ConsumerState<AlbumShowPhotoPage> {
  late final PageController pageController;
  late final int initialPage;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    initialPage = ref.read(albumControllerProvider.notifier).checkCurrentPhotoIndex(widget.currentPhoto);
    _currentPage = initialPage; // 初始化当前页面索引
    pageController = PageController(initialPage: initialPage);
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final count = ref.watch(albumControllerProvider).value?.length ?? 0;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
      ),
      body: PhotoViewGallery.builder(
        pageController: pageController,
        scrollPhysics: const BouncingScrollPhysics(),
        gaplessPlayback: true,
        allowImplicitScrolling: true,
        itemCount: count,
        builder: _buildItem,
        onPageChanged: (index) {
          setState(() {
            _currentPage = index; // 更新当前页面索引
          });
        },
      ),
    );
  }

  PhotoViewGalleryPageOptions _buildItem(BuildContext context, int index) {
    final controller = ref.read(albumControllerProvider.notifier);
    final length = controller.length;
    if (controller.hasMore && index + 2 >= length) {
      controller.loadMore();
    }

    final photo = controller.getPhoto(index);

    return PhotoViewGalleryPageOptions.customChild(
      child: MediaRenderer(
        path: photo!.file,
        mediaType: photo.mediaType,
        isVisible: index == _currentPage, // 只有当前页面的视频才会播放
      ),
      minScale: PhotoViewComputedScale.contained,
      maxScale: PhotoViewComputedScale.covered * 2,

    );
  }
}
