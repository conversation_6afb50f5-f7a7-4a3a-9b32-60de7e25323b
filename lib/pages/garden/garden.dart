import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/current_home_view_mode.dart';
import 'package:flower_timemachine/widgets/error_reporter.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import 'package:flower_timemachine/controller/tag_list_controller.dart';
import 'package:flower_timemachine/models/tag_info.dart';
import 'package:flower_timemachine/widgets/tag_info_container.dart';
import 'grid_view/garden_grid.dart';
import 'list_view/garden_list.dart';

class GardenTagPage extends ConsumerStatefulWidget {
  const GardenTagPage({super.key});

  @override
  ConsumerState createState() => _GardenTagWidgetState();
}

class _GardenTagWidgetState extends ConsumerState<GardenTagPage> with TickerProviderStateMixin {
  TabController? tabController;

  @override
  Widget build(BuildContext context) {
    final viewMode = ref.watch(currentHomeViewModeProvider);
    debugPrint("GardenTagPage build: $viewMode");
    return Column(
      children: [
        // 标签栏
        Consumer(
            builder: (context, ref, _) => ref.watch(tagListControllerProvider).when(
                error: (error, stackTrace) => ErrorReporter(error: error, stackTrace: stackTrace),
                loading: () => Container(
                    height: 40,
                    alignment: Alignment.center,
                    child: const SizedBox(height: 20, width: 20, child: CircularProgressIndicator())),
                data: (data) {
                  // 没有标签的时候不显示
                  if (data.isEmpty) {
                    return const SizedBox.shrink();
                  }
                  if (tabController != null) {
                    tabController!.dispose();
                    tabController = null;
                  }

                  final currentTag = ref.read(currentTagProvider);
                  // 加一个显示所有的标签
                  int initialIndex = data.indexOf(currentTag) + 1;

                  tabController = TabController(initialIndex: initialIndex, length: data.length + 1, vsync: this);
                  return Container(
                      height: 40,
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      alignment: Alignment.centerLeft,
                      child: TabBar(
                        controller: tabController,
                        isScrollable: true,
                        indicatorSize: TabBarIndicatorSize.label,
                        indicator: BoxDecoration(
                          color: const Color(0xFF339900),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        labelColor: Colors.white,
                        unselectedLabelColor: const Color(0xFF339900),
                        labelPadding: const EdgeInsets.symmetric(horizontal: 8),
                        tabs: _buildTagItems(data),
                        onTap: onTapTag,
                      ));
                })),
        // 花列表
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(left: 10, right: 10, top: 10),
            child: viewMode == HomeViewMode.grid ? const GardenGird() : const GardenList(),
          ),
        )
      ],
    );
  }

  List<Widget> _buildTagItems(Iterable<TagInfo> tagInfoList) {
    List<Widget> result = [TagInfoContainer(tagName: allTag.name.tr())];

    for (final tag in tagInfoList) {
      result.add(TagInfoContainer(tagName: tag.name));
    }

    return result;
  }

  void onTapTag(int index) async {
    // 第一个是“所有”，见 _buildTagItems
    if (index == 0) {
      ref.read(currentTagProvider.notifier).state = allTag;
      return;
    }

    index -= 1;
    final tags = ref.read(tagListControllerProvider).value;
    final tag = tags?.elementAt(index);
    if (tag != null) {
      ref.read(currentTagProvider.notifier).state = tag;
    }
  }

  @override
  void dispose() {
    tabController?.dispose();
    super.dispose();
  }
}
