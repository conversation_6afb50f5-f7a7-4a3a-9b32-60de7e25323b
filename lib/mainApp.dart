import 'dart:io';

import 'package:flower_timemachine/models/flower_timeline_editor_item.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/models/photo_record.dart';
import 'package:flower_timemachine/models/tag_info.dart';
import 'package:flower_timemachine/pages/add_flower/add_flower_new.dart';
import 'package:flower_timemachine/pages/add_nurture_type/add_nurture_type.dart';
import 'package:flower_timemachine/pages/archive/archive.dart';
import 'package:flower_timemachine/pages/archive_info/archive_info_page.dart';
import 'package:flower_timemachine/pages/backup/backup.dart';
import 'package:flower_timemachine/pages/flower_share/flower_share.dart';
import 'package:flower_timemachine/pages/flower_timeline_editor/flower_timeline_editor.dart';
import 'package:flower_timemachine/pages/flower_timeline_editor/show_photo.dart';
import 'package:flower_timemachine/pages/nurture_manager/nurture_manager.dart';
import 'package:flower_timemachine/pages/remind_help/remind_help.dart';
import 'package:flower_timemachine/pages/remind_manager/remind_manager.dart';
import 'package:flower_timemachine/pages/tag_selector/tag_selector.dart';
import 'package:flower_timemachine/pages/avatar_editor/avatar_editor.dart';
import 'package:flower_timemachine/pages/flower_detail/flower_detail.dart';
import 'package:flower_timemachine/pages/show_photo/show_photo.dart';
import 'package:flower_timemachine/pages/tag_manager/tag_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:posthog_flutter/posthog_flutter.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:showcaseview/showcaseview.dart';

import 'models/flower.dart';
import 'pages/album/show_photo.dart';
import 'pages/home/<USER>';
import 'pages/search/search.dart';

class MainAPP extends StatelessWidget {
  const MainAPP({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        useMaterial3: false,
        primarySwatch: createMaterialColor(const Color(0xFF339900)),
        tabBarTheme: const TabBarTheme(labelColor: Color(0xFF339900), unselectedLabelColor: Colors.black54),
        splashColor: Colors.transparent,
        appBarTheme: const AppBarTheme(
            backgroundColor: Colors.white,
            iconTheme: IconThemeData(color: Color(0xFF339900), size: 30),
            titleTextStyle: TextStyle(color: Color(0xFF339900), fontSize: 20)),
        textTheme: const TextTheme(
          bodyMedium: TextStyle(color: Color(0xFF339900)),
        ),
        iconTheme: const IconThemeData(color: Color(0xFF339900)),
        scaffoldBackgroundColor: Colors.white,
      ),
      supportedLocales: context.supportedLocales,
      localizationsDelegates: context.localizationDelegates,
      locale: context.locale,
      initialRoute: 'home',
      onGenerateRoute: onGenerateRoute,
      builder: EasyLoading.init(),
    );
  }

  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    if (settings.name != null) {
      Posthog().screen(screenName: settings.name!);
    }

    Widget page;
    switch (settings.name) {
      case 'home':
        {
          page = const HomePage();
        }
      case 'add_flower':
        {
          page = AddFlowerPage(flower: settings.arguments as Flower?);
        }
      case 'edit_image':
        {
          page = AvatarEditorPage(imagePath: settings.arguments as File);
        }
      case 'flower_detail':
        {
          List arguments = settings.arguments as List;
          page = FlowerDetailPage(
            flower: arguments[0],
            resultRef: arguments[1],
          );
        }
      case 'show_photo':
        {
          List arguments = settings.arguments as List;
          page = ShowPhotoPage(
            currentPhotoRecord: arguments[0],
            flowerTimelines: arguments[1],
            flowerId: arguments[2],
          );
        }
      case TagManagerPage.routeName:
        {
          page = ShowCaseWidget(
            builder: (context) => const TagManagerPage(),
          );
        }
      case TagSelectorPage.routeName:
        {
          page = TagSelectorPage(selectedTags: settings.arguments as List<TagInfo>);
        }
      case FlowerTimelineEditor.routeName:
        {
          page = FlowerTimelineEditor(
            timeline: settings.arguments as FlowerTimelineEditorItem?,
          );
        }
      case FlowerTimelineEditorShowPhoto.routeName:
        {
          final args = settings.arguments as List<dynamic>;
          page = FlowerTimelineEditorShowPhoto(
            photos: args[0],
            initialPage: args[1],
          );
        }
      case FlowerShare.routeName:
        {
          page = FlowerShare(
            flower: settings.arguments as Flower,
          );
        }
      case RemindManager.routeName:
        {
          page = const RemindManager();
        }
      case RemindHelp.routeName:
        {
          page = const RemindHelp();
        }
      case Search.routeName:
        {
          page = const Search();
        }
      case NurtureManager.routeName:
        {
          page = const NurtureManager();
        }
      case AddNurtureType.routeName:
        {
          page = AddNurtureType(
            type: settings.arguments as NurtureType?,
          );
        }
      case Backup.routeName:
        {
          page = const Backup();
        }
      case Archive.routeName:
        {
          page = const Archive();
        }
      case ArchiveInfoPage.routeName:
        {
          page = ArchiveInfoPage(flower: settings.arguments as Flower);
        }
      case AlbumShowPhotoPage.routeName:
        {
          PhotoRecord photo = settings.arguments as PhotoRecord;
          page = AlbumShowPhotoPage(
            currentPhoto: photo,
          );
        }
      default:
        {
          Posthog().capture(eventName: 'unknown page', properties: {'page': settings.name as String});
          return null;
        }
    }

    return MaterialPageRoute(builder: (context) => page);
  }
}

MaterialColor createMaterialColor(Color color) {
  List strengths = <double>[.05];
  Map<int, Color> swatch = {};
  final int r = color.red, g = color.green, b = color.blue;

  for (int i = 1; i < 10; i++) {
    strengths.add(0.1 * i);
  }
  for (var strength in strengths) {
    final double ds = 0.5 - strength;
    swatch[(strength * 1000).round()] = Color.fromRGBO(
      r + ((ds < 0 ? r : (255 - r)) * ds).round(),
      g + ((ds < 0 ? g : (255 - g)) * ds).round(),
      b + ((ds < 0 ? b : (255 - b)) * ds).round(),
      1,
    );
  }
  ;
  return MaterialColor(color.value, swatch);
}
