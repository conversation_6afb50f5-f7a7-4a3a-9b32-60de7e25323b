// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'flower_timeline_editor_item.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$FlowerTimelineEditorItem {
  String? get text => throw _privateConstructorUsedError;
  List<MediaItem> get mediaItems => throw _privateConstructorUsedError;
  DateTime get dateTime => throw _privateConstructorUsedError;

  /// Create a copy of FlowerTimelineEditorItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $FlowerTimelineEditorItemCopyWith<FlowerTimelineEditorItem> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FlowerTimelineEditorItemCopyWith<$Res> {
  factory $FlowerTimelineEditorItemCopyWith(FlowerTimelineEditorItem value,
          $Res Function(FlowerTimelineEditorItem) then) =
      _$FlowerTimelineEditorItemCopyWithImpl<$Res, FlowerTimelineEditorItem>;
  @useResult
  $Res call({String? text, List<MediaItem> mediaItems, DateTime dateTime});
}

/// @nodoc
class _$FlowerTimelineEditorItemCopyWithImpl<$Res,
        $Val extends FlowerTimelineEditorItem>
    implements $FlowerTimelineEditorItemCopyWith<$Res> {
  _$FlowerTimelineEditorItemCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of FlowerTimelineEditorItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = freezed,
    Object? mediaItems = null,
    Object? dateTime = null,
  }) {
    return _then(_value.copyWith(
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      mediaItems: null == mediaItems
          ? _value.mediaItems
          : mediaItems // ignore: cast_nullable_to_non_nullable
              as List<MediaItem>,
      dateTime: null == dateTime
          ? _value.dateTime
          : dateTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FlowerTimelineEditorItemImplCopyWith<$Res>
    implements $FlowerTimelineEditorItemCopyWith<$Res> {
  factory _$$FlowerTimelineEditorItemImplCopyWith(
          _$FlowerTimelineEditorItemImpl value,
          $Res Function(_$FlowerTimelineEditorItemImpl) then) =
      __$$FlowerTimelineEditorItemImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? text, List<MediaItem> mediaItems, DateTime dateTime});
}

/// @nodoc
class __$$FlowerTimelineEditorItemImplCopyWithImpl<$Res>
    extends _$FlowerTimelineEditorItemCopyWithImpl<$Res,
        _$FlowerTimelineEditorItemImpl>
    implements _$$FlowerTimelineEditorItemImplCopyWith<$Res> {
  __$$FlowerTimelineEditorItemImplCopyWithImpl(
      _$FlowerTimelineEditorItemImpl _value,
      $Res Function(_$FlowerTimelineEditorItemImpl) _then)
      : super(_value, _then);

  /// Create a copy of FlowerTimelineEditorItem
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? text = freezed,
    Object? mediaItems = null,
    Object? dateTime = null,
  }) {
    return _then(_$FlowerTimelineEditorItemImpl(
      text: freezed == text
          ? _value.text
          : text // ignore: cast_nullable_to_non_nullable
              as String?,
      mediaItems: null == mediaItems
          ? _value._mediaItems
          : mediaItems // ignore: cast_nullable_to_non_nullable
              as List<MediaItem>,
      dateTime: null == dateTime
          ? _value.dateTime
          : dateTime // ignore: cast_nullable_to_non_nullable
              as DateTime,
    ));
  }
}

/// @nodoc

class _$FlowerTimelineEditorItemImpl implements _FlowerTimelineEditorItem {
  const _$FlowerTimelineEditorItemImpl(
      {required this.text,
      required final List<MediaItem> mediaItems,
      required this.dateTime})
      : _mediaItems = mediaItems;

  @override
  final String? text;
  final List<MediaItem> _mediaItems;
  @override
  List<MediaItem> get mediaItems {
    if (_mediaItems is EqualUnmodifiableListView) return _mediaItems;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_mediaItems);
  }

  @override
  final DateTime dateTime;

  @override
  String toString() {
    return 'FlowerTimelineEditorItem(text: $text, mediaItems: $mediaItems, dateTime: $dateTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FlowerTimelineEditorItemImpl &&
            (identical(other.text, text) || other.text == text) &&
            const DeepCollectionEquality()
                .equals(other._mediaItems, _mediaItems) &&
            (identical(other.dateTime, dateTime) ||
                other.dateTime == dateTime));
  }

  @override
  int get hashCode => Object.hash(runtimeType, text,
      const DeepCollectionEquality().hash(_mediaItems), dateTime);

  /// Create a copy of FlowerTimelineEditorItem
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$FlowerTimelineEditorItemImplCopyWith<_$FlowerTimelineEditorItemImpl>
      get copyWith => __$$FlowerTimelineEditorItemImplCopyWithImpl<
          _$FlowerTimelineEditorItemImpl>(this, _$identity);
}

abstract class _FlowerTimelineEditorItem implements FlowerTimelineEditorItem {
  const factory _FlowerTimelineEditorItem(
      {required final String? text,
      required final List<MediaItem> mediaItems,
      required final DateTime dateTime}) = _$FlowerTimelineEditorItemImpl;

  @override
  String? get text;
  @override
  List<MediaItem> get mediaItems;
  @override
  DateTime get dateTime;

  /// Create a copy of FlowerTimelineEditorItem
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$FlowerTimelineEditorItemImplCopyWith<_$FlowerTimelineEditorItemImpl>
      get copyWith => throw _privateConstructorUsedError;
}
