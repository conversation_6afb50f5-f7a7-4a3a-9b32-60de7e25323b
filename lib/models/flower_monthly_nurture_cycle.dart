import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:flower_timemachine/types/flower_nurture_cycle.dart';
import 'package:sqflite/sqflite.dart';

class FlowerMonthlyNurtureCycle {
  static const tableName = 'flower_monthly_nurture_cycle';

  final int id;
  final int flowerId;
  final int typeId;
  final int month; // 月份 1-12
  final int cycle; // 养护时间间隔（单位：天）

  FlowerMonthlyNurtureCycle({
    required this.id,
    required this.flowerId,
    required this.typeId,
    required this.month,
    required this.cycle,
  });

  static String createTable() {
    return 'CREATE TABLE $tableName('
        'id INTEGER PRIMARY KEY, '
        'flowerId INTEGER, '
        'typeId INTEGER, '
        'month INTEGER, '
        'cycle INTEGER, '
        'UNIQUE(flowerId, typeId, month))';
  }

  static Future<List<FlowerMonthlyNurtureCycle>> getByFlowerAndType(int flowerId, int typeId) async {
    final db = await DB.get();
    final List<Map<String, dynamic>> rows = await db.sqlite.query(
      tableName,
      where: "flowerId = ? AND typeId = ?",
      whereArgs: [flowerId, typeId],
      orderBy: "month ASC",
    );

    return rows.map((row) => FlowerMonthlyNurtureCycle(
      id: row['id'],
      flowerId: row['flowerId'],
      typeId: row['typeId'],
      month: row['month'],
      cycle: row['cycle'],
    )).toList();
  }

  static Future<Map<int, int>> getMonthCycleMap(int flowerId, int typeId) async {
    final cycles = await getByFlowerAndType(flowerId, typeId);
    final Map<int, int> result = {};
    for (final cycle in cycles) {
      result[cycle.month] = cycle.cycle;
    }
    return result;
  }

  static Future<int?> getCycleForMonth(int flowerId, int typeId, int month) async {
    final db = await DB.get();
    final List<Map<String, dynamic>> rows = await db.sqlite.query(
      tableName,
      where: "flowerId = ? AND typeId = ? AND month = ?",
      whereArgs: [flowerId, typeId, month],
      limit: 1,
    );

    if (rows.isEmpty) {
      return null;
    }
    return rows.first['cycle'] as int;
  }

  static Future<int> getEffectiveCycleForMonth(int flowerId, int typeId, int month) async {
    // 先查找当前月份的设置
    final currentMonthCycle = await getCycleForMonth(flowerId, typeId, month);
    if (currentMonthCycle != null) {
      return currentMonthCycle;
    }

    // 只往前查找最近的月份设置（不跨年）
    final db = await DB.get();
    final List<Map<String, dynamic>> rows = await db.sqlite.query(
      tableName,
      where: "flowerId = ? AND typeId = ? AND month < ?",
      whereArgs: [flowerId, typeId, month],
      orderBy: "month DESC",
      limit: 1,
    );

    if (rows.isNotEmpty) {
      return rows.first['cycle'] as int;
    }

    // 前面没有设置，使用默认周期
    final controller = NurtureTypesController.get();
    final type = controller.getTypeInfo(typeId);
    return type?.defaultCycle ?? 0;
  }

  static Future<void> setCycleForMonth(int flowerId, int typeId, int month, int cycle) async {
    final db = await DB.get();

    if (cycle <= 0) {
      // 删除设置
      await db.sqlite.delete(
        tableName,
        where: "flowerId = ? AND typeId = ? AND month = ?",
        whereArgs: [flowerId, typeId, month],
      );
    } else {
      // 插入或更新设置
      await db.sqlite.insert(
        tableName,
        {
          'flowerId': flowerId,
          'typeId': typeId,
          'month': month,
          'cycle': cycle,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
  }

  static Future<void> deleteByFlower(int flowerId) async {
    final db = await DB.get();
    await db.sqlite.delete(tableName, where: 'flowerId = ?', whereArgs: [flowerId]);
  }

  static Future<void> deleteByNurtureType(int typeId) async {
    final db = await DB.get();
    await db.sqlite.delete(tableName, where: 'typeId = ?', whereArgs: [typeId]);
  }

  static Future<void> createFromNurtureCycles(int flowerId, List<FlowerNurtureCycle> nurtureCycles) async {
    final db = await DB.get();
    final batch = db.sqlite.batch();

    for (final cycle in nurtureCycles) {
      if (cycle.cycle > 0) {
        // 将周期作为1月的数据插入
        batch.insert(
          tableName,
          {
            'flowerId': flowerId,
            'typeId': cycle.type.id,
            'month': 1,
            'cycle': cycle.cycle,
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    }

    await batch.commit(noResult: true);
  }

  static Future<void> updateFromNurtureCycles(int flowerId, List<FlowerNurtureCycle> nurtureCycles) async {
    // 对于更新，我们只更新1月的数据，保持其他月份的设置
    final db = await DB.get();
    final batch = db.sqlite.batch();

    // 删除1月的数据
    batch.delete(tableName, where: 'flowerId = ? AND month = ?', whereArgs: [flowerId, 1]);

    // 插入新1月数据
    for (final cycle in nurtureCycles) {
      if (cycle.cycle > 0) {
        batch.insert(
          tableName,
          {
            'flowerId': flowerId,
            'typeId': cycle.type.id,
            'month': 1,
            'cycle': cycle.cycle,
          },
          conflictAlgorithm: ConflictAlgorithm.replace,
        );
      }
    }

    await batch.commit(noResult: true);
  }
}
