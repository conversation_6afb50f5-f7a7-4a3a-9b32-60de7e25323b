import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/controller/maintenance_type.dart';
import 'package:sqflite/sqflite.dart';

class NurtureType {
  static const tableName = 'nurture_types';

  final int id;
  String _name;
  String _icon;
  bool _enable;
  // 默认养护周期（单位：天）
  int _defaultCycle;
  final int createTime;

  static const idField = "id";
  static const nameField = "name";
  static const iconField = "icon";
  static const enableField = "enable";
  static const defaultCycleField = "defaultCycle";
  static const createTimeField = "createTime";

  NurtureType(this.id, String name, String icon, bool enable, int defaultCycle, this.createTime)
      : _name = name,
        _icon = icon,
        _enable = enable,
        _defaultCycle = defaultCycle;

  static String createTable() {
    return 'CREATE TABLE $tableName('
        'id INTEGER PRIMARY KEY, '
        'name TEXT, '
        'icon TEXT, '
        'enable int, '
        'defaultCycle int, '
        'createTime INTEGER)';
  }

  static Future<void> createDefaultTypes(Database db) async {
    final batch = db.batch();
    batch.insert(tableName, {
      "id": MaintenanceType.watering.index,
      "name": MaintenanceType.watering.toText(),
      "icon": "icons/water.svg",
      "enable": 1,
      "defaultCycle": 7,
      "createTime": DateTime.now().millisecondsSinceEpoch
    });
    batch.insert(tableName, {
      "id": MaintenanceType.fertilize.index,
      "name": MaintenanceType.fertilize.toText(),
      "icon": "icons/fertilizer.svg",
      "enable": 1,
      "createTime": DateTime.now().millisecondsSinceEpoch
    });
    batch.insert(tableName, {
      "id": MaintenanceType.pruning.index,
      "name": MaintenanceType.pruning.toText(),
      "icon": "icons/cut.svg",
      "enable": 1,
      "createTime": DateTime.now().millisecondsSinceEpoch
    });
    batch.insert(tableName, {
      "id": MaintenanceType.pestControl.index,
      "name": MaintenanceType.pestControl.toText(),
      "icon": "icons/pest_control.svg",
      "enable": 1,
      "createTime": DateTime.now().millisecondsSinceEpoch
    });

    await batch.commit();
  }

  static Future<Iterable<NurtureType>> getAll({bool includeDisable = false}) async {
    final db = await DB.get();

    String sql = "SELECT * FROM $tableName ";
    if (!includeDisable) {
      sql += "WHERE enable = 1 ";
    }
    sql += "ORDER BY \"createTime\" ASC";

    List<Map<String, dynamic>> result = await db.sqlite.rawQuery(sql);

    return result.map(
        (e) => NurtureType(e['id'], e['name'], e['icon'], e['enable'] == 1, (e['defaultCycle'] ?? 0), e['createTime']));
  }

  Future<void> update({
    String? name,
    String? icon,
    bool? enable,
    int? defaultCycle,
  }) async {
    Map<String, dynamic> updateMap = {};
    if (name != null && name != _name) {
      _name = name;
      updateMap["name"] = name;
    }
    if (icon != null && icon != _icon) {
      _icon = icon;
      updateMap["icon"] = icon;
    }
    if (enable != null && enable != _enable) {
      _enable = enable;
      updateMap["enable"] = enable ? 1 : 0;
    }
    if (defaultCycle != _defaultCycle) {
      _defaultCycle = defaultCycle ?? 0;
      updateMap["defaultCycle"] = defaultCycle;
    }

    if (updateMap.isEmpty) {
      return;
    }

    final db = await DB.get();

    await db.sqlite.update(tableName, updateMap, where: "id = ?", whereArgs: [id]);
  }

  static Future<NurtureType> create({
    required String name,
    required String icon,
    int? defaultCycle,
    bool? enable,
  }) async {
    final db = await DB.get();
    final createTime = DateTime.now().millisecondsSinceEpoch;
    final id = await db.sqlite.insert(tableName,
        {"name": name, "icon": icon, "defaultCycle": defaultCycle, "enable": enable ?? true, "createTime": createTime});

    return NurtureType(id, name, icon, enable ?? true, defaultCycle ?? 0, createTime);
  }

  Future<void> delete() async {
    final db = await DB.get();
    await db.sqlite.delete(tableName, where: "id = ?", whereArgs: [id]);
  }

  @override
  bool operator ==(Object other) {
    return other is NurtureType &&
        other.id == id &&
        other._name == _name &&
        other._icon == _icon &&
        other.enable == enable &&
        other.createTime == createTime;
  }

  @override
  int get hashCode => Object.hash(id, _name, _icon, _enable, createTime);

  String get name {
    // 对于用户修改了名称，不使用默认的名称
    if (!_name.startsWith("care_type.")) {
      return _name;
    } else if (id < MaintenanceType.values.length) {
      // 兼容旧情况
      return MaintenanceType.values[id].toText();
    } else {
      return _name;
    }
  }

  String get icon => _icon;
  bool get enable => _enable;
  int get defaultCycle => _defaultCycle;
}
