import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:sqflite/sqflite.dart';

import '../common/global.dart';
import '../common/errors.dart';
import 'nurture_types.dart';

class MaintenanceRecord {
  MaintenanceRecord({int? id, required this.flowerId, required this.type, required this.time, this.remark}) : _id = id;

  static const tableName = 'maintenance_records';

  int? _id;
  final int flowerId;
  final NurtureType type;
  final String? remark;

  // 秒
  final int time;

  static const idField = "id";
  static const flowerIdField = "flowerId";
  static const typeField = "type";
  static const remarkField = "remark";
  static const timeField = "time";

  static String createTable() {
    return 'CREATE TABLE $tableName('
        'id INTEGER PRIMARY KEY, '
        'flowerId INTEGER, '
        'type INTEGER,'
        'remark TEXT,'
        'time INTEGER)';
  }

  Future<int> insert() async {
    final db = await DB.get();

    // 检测今天是否已经插入了数据
    final count = Sqflite.firstIntValue(await db.sqlite.rawQuery(
        'SELECT COUNT(*) AS count '
        'FROM $tableName '
        'WHERE type = ?'
        'AND '
        'flowerId = ?'
        'AND '
        'DATE(time, \'UNIXEPOCH\', \'LOCALTIME\') = DATE(?, \'UNIXEPOCH\', \'LOCALTIME\')',
        [type.id, flowerId, time]));

    if ((count ?? 0) > 0) {
      return Errors.exist;
    }

    // 插入
    final ret = await db.sqlite.insert(tableName, {
      'flowerId': flowerId,
      'type': type.id,
      'time': time,
      'remark': remark,
    });
    _id = ret;

    return ret;
  }

  static Future<int> insertByType(int flowerId, NurtureType type, DateTime time, String? remark) async {
    final record = MaintenanceRecord(
      flowerId: flowerId,
      type: type,
      time: (time.millisecondsSinceEpoch / 1000).truncate(),
      remark: remark,
    );
    return await record.insert();
  }

  static Future<List<MaintenanceRecord>> batchInsertByTypes(
      int flowerId, List<NurtureType> types, int time, String? remark) async {
    final db = await DB.get();

    // 筛选今天没有插入的类型
    final currentTypes = await db.sqlite.rawQuery(
      'SELECT type FROM $tableName '
      'WHERE flowerId = ? '
      'AND '
      'DATE(time, \'UNIXEPOCH\', \'LOCALTIME\') = DATE(?, \'UNIXEPOCH\', \'LOCALTIME\')',
      [flowerId, time],
    );

    final typesSet = types.map((e) => e.id).toSet();
    final dbTypesSet = currentTypes.map((e) => e['type']).toSet();
    final needInsertTypes = typesSet.difference(dbTypesSet);

    if (needInsertTypes.isEmpty) {
      return [];
    }

    final batch = db.sqlite.batch();
    for (final type in needInsertTypes) {
      batch.rawInsert(
          'INSERT INTO $tableName(flowerId, type, time, remark) VALUES (?, ?, ?, ?)', [flowerId, type, time, remark]);
    }

    final batchRet = await batch.commit();
    final successTypes = <MaintenanceRecord>[];
    int index = 0;
    for (final type in needInsertTypes) {
      final ret = batchRet[index] as int?;
      index += 1;
      if (ret == null || ret <= 0) {
        continue;
      }

      final typeInfo = NurtureTypesController.get().getTypeInfo(type);
      if (typeInfo == null) {
        continue;
      }

      successTypes.add(MaintenanceRecord(id: ret, flowerId: flowerId, type: typeInfo, time: time, remark: remark));
    }

    return successTypes;
  }

  Future<int> delete() async {
    final db = await DB.get();

    return await db.sqlite.delete(tableName, where: 'id = ?', whereArgs: [id!]);
  }

  static Future<void> deleteByFlower(int flowerId) async {
    final db = await DB.get();
    await db.sqlite.delete(tableName, where: 'flowerId = ?', whereArgs: [flowerId]);
  }

  static Future<List<MaintenanceRecord>> get(int flowerId, {required int offset, required int limit}) async {
    final db = await DB.get();

    List<Map<String, dynamic>> ret = await db.sqlite.query(tableName,
        where: 'flowerId = ?', whereArgs: [flowerId], orderBy: 'time DESC', limit: limit, offset: offset);

    List<MaintenanceRecord> records = [];
    for (final row in ret) {
      final typeInfo = NurtureTypesController.get().getTypeInfo(row['type']);
      if (typeInfo == null) {
        continue;
      }

      records.add(
        MaintenanceRecord(
          id: row['id'],
          flowerId: row['flowerId'],
          type: typeInfo,
          time: row['time'],
          remark: row['remark'],
        ),
      );
    }

    return records;
  }

  /// 获取最近一天的养护记录
  static Future<List<MaintenanceRecord>> getLatestDay(int flowerId) async {
    final db = await DB.get();

    // 使用单条 SQL 查询最近一天的所有记录
    final List<Map<String, dynamic>> ret = await db.sqlite.rawQuery(
      'SELECT * FROM $tableName '
      'WHERE flowerId = ? '
      'AND DATE(time, \'UNIXEPOCH\', \'LOCALTIME\') = '
      '(SELECT DATE(time, \'UNIXEPOCH\', \'LOCALTIME\') '
      'FROM $tableName '
      'WHERE flowerId = ? '
      'ORDER BY time DESC LIMIT 1)',
      [flowerId, flowerId],
    );

    List<MaintenanceRecord> records = [];
    for (final row in ret) {
      final typeInfo = NurtureTypesController.get().getTypeInfo(row['type']);
      if (typeInfo == null) {
        continue;
      }

      records.add(
        MaintenanceRecord(
          id: row['id'],
          flowerId: row['flowerId'],
          type: typeInfo,
          time: row['time'],
          remark: row['remark'],
        ),
      );
    }

    return records;
  }

  static Future<List<int>> getRowNumber(int flowerId, List<int> ids) async {
    final db = await DB.get();

    final List<Map<String, dynamic>> rows = await db.sqlite.rawQuery(
        'SELECT row from '
        '(SELECT ROW_NUMBER() OVER (ORDER BY "time" DESC) as row, id from $tableName WHERE flowerId = ?) '
        'WHERE id in (${ids.join(',')})',
        [flowerId]);

    return rows.map((e) => e['row'] as int).toList();
  }

  static Future<void> deleteByNurtureType(int nurtureTypeId) async {
    final db = await DB.get();
    await db.sqlite.delete(tableName, where: 'type = ?', whereArgs: [nurtureTypeId]);
  }

  static MaintenanceRecord fromDB(Map<String, dynamic> row) {
    return MaintenanceRecord(
      id: row['id'],
      flowerId: row['flowerId'],
      type: NurtureTypesController.get().getTypeInfo(row['type'])!,
      time: row['time'],
      remark: row['remark'],
    );
  }

  int? get id {
    return _id;
  }
}
