import 'dart:io';

import 'package:flower_timemachine/common/file_utils.dart';
import 'package:sqflite/sqflite.dart';

import '../common/global.dart';
import 'media_item.dart';

enum MediaType {
  image,
  video,
  livePhoto,
}

class PhotoRecord {
  int? id;
  late String _file;
  String? _thumbnailFile; // 缩略图路径

  // 对应哪朵花
  final int flowerId;

  // 对应哪朵花下的时光记录
  final int flowerTimelineId;

  // 秒
  final int time;

  // 媒体类型
  final MediaType mediaType;

  static const tableName = "photo_records";

  static const idField = "id";
  static const fileField = "file";
  static const thumbnailFileField = "thumbnailFile";
  static const flowerIdField = "flowerId";
  static const flowerTimelineIdField = "flowerTimelineId";
  static const timeField = "time";
  static const mediaTypeField = "mediaType";

  PhotoRecord({
    this.id,
    required this.flowerId,
    required String file,
    String? thumbnailFile,
    required this.flowerTimelineId,
    required this.time,
    this.mediaType = MediaType.image,
  }) {
    _file = file;
    _thumbnailFile = thumbnailFile;
  }

  static String createTable() {
    return 'CREATE TABLE $tableName('
        'id INTEGER PRIMARY KEY, '
        'flowerId INTEGER, '
        'flowerTimelineId INTEGER, '
        'file String,'
        'thumbnailFile String,'
        'time INTEGER,'
        'mediaType INTEGER DEFAULT 0)';
  }

  static PhotoRecord fromDB(Map<String, dynamic> row) {
    return PhotoRecord(
      id: row['id'],
      flowerId: row['flowerId'],
      file: row['file'],
      thumbnailFile: row['thumbnailFile'],
      flowerTimelineId: row['flowerTimelineId'],
      time: row['time'],
      mediaType: row['mediaType'] != null
          ? MediaType.values[row['mediaType']]
          : MediaType.image,
    );
  }

  static Future<List<PhotoRecord>> getByFlowerTimelineId(int flowerTimelineId) async {
    final db = await DB.get();

    List<Map<String, dynamic>> results = await db.sqlite
        .query(tableName, where: 'flowerTimelineId = ?', whereArgs: [flowerTimelineId], orderBy: 'time DESC');

    return results.map((e) => PhotoRecord.fromDB(e)).toList();
  }

  static Future<List<PhotoRecord>> getByFlowerId(int flowerId,
      {int? offset, required int limit, required String timeOrder}) async {
    final db = await DB.get();

    List<Map<String, dynamic>> results = await db.sqlite.query(tableName,
        where: 'flowerId = ?', whereArgs: [flowerId], offset: offset, limit: limit, orderBy: 'time $timeOrder');

    return results.map((e) => PhotoRecord.fromDB(e)).toList();
  }

  static Future<List<PhotoRecord>> getByFlowerIdExcludeVideo(int flowerId,
      {int? offset, required int limit, required String timeOrder}) async {
    final db = await DB.get();

    List<Map<String, dynamic>> results = await db.sqlite.query(
      tableName,
      where: 'flowerId = ? AND mediaType != ?',
      whereArgs: [flowerId, MediaType.video.index],
      offset: offset,
      limit: limit,
      orderBy: 'time $timeOrder'
    );

    return results.map((e) => PhotoRecord.fromDB(e)).toList();
  }

  static Future<int?> getRowIndex(PhotoRecord currentPos) async {
    final db = await DB.get();

    return Sqflite.firstIntValue(await db.sqlite.rawQuery(
        'SELECT row from '
        '(SELECT ROW_NUMBER() OVER (ORDER BY "time" DESC) as row, * from $tableName WHERE flowerId = ?) '
        'WHERE id = ?',
        [currentPos.flowerId, currentPos.id]));
  }

  Future<int> insert() async {
    final db = await DB.get();

    // 插入
    return await db.sqlite.insert(tableName, {
      'flowerId': flowerId,
      'file': _file,
      'thumbnailFile': _thumbnailFile,
      'time': time,
      'mediaType': mediaType.index,
    });
  }

  Future<int> delete() async {
    final db = await DB.get();

    return await db.sqlite.delete(tableName, where: 'id = ?', whereArgs: [id!]);
  }

  static Future<void> deleteByFlowerId(int flowerId) async {
    final db = await DB.get();

    final List<Map<String, dynamic>> result = await db.sqlite.rawQuery(
        'SELECT file, '
        '(SELECT COUNT(DISTINCT flowerId) FROM $tableName b WHERE b."file" = a."file" GROUP BY b."file") as c '
        'from $tableName a WHERE a.flowerId = ?',
        [flowerId]);

    for (final row in result) {
      // 只有当前 flower id 引用这个文件
      if (row['c'] == 1) {
        final f = File('${Global.photoRecordDir}/${row['file']}');
        if (await f.exists()) {
          await f.delete();
        }
      }
    }

    await db.sqlite.delete(tableName, where: 'flowerId = ?', whereArgs: [flowerId]);
  }

  static Future<void> deleteByFlowerTimelineId(int flowerTimelineId) async {
    final db = await DB.get();

    final List<Map<String, dynamic>> result = await db.sqlite.rawQuery(
        'SELECT file, thumbnailFile, '
        '(SELECT COUNT(DISTINCT flowerTimelineId) FROM $tableName b WHERE b."file" = a."file" GROUP BY b."file") as c '
        'from $tableName a WHERE a.flowerTimelineId = ?',
        [flowerTimelineId]);

    for (final row in result) {
      // 只有当前 timeline id 引用这个文件
      if (row['c'] == 1) {
        final f = File('${Global.photoRecordDir}/${row['file']}');
        if (await f.exists()) {
          await f.delete();
        }
        final thumbnailFile = row['thumbnailFile'] as String?;
        if (thumbnailFile != null) {
          final f = File('${Global.photoRecordDir}/$thumbnailFile');
          if (await f.exists()) {
            await f.delete();
          }
        }
      }
    }

    await db.sqlite.delete(tableName, where: 'flowerTimelineId = ?', whereArgs: [flowerTimelineId]);
  }

  static Future<List<PhotoRecord>> createList(
      int flowerId, int flowerTimelineId, List<MediaItem> photos, DateTime time
  ) async {
    List<PhotoRecord> savedRecords = [];
    final db = await DB.get();
    final batch = db.sqlite.batch();
    for (final record in photos.reversed) {
      final newPath = await copyToPhotoRecordDir(record.path);

      // 处理缩略图
      String? thumbnailPath;
      if (record.thumbnailPath != null) {
        thumbnailPath = await copyToPhotoRecordDir(record.thumbnailPath!);
      }

      final newRecord = PhotoRecord(
          flowerId: flowerId,
          file: newPath,
          thumbnailFile: thumbnailPath,
          flowerTimelineId: flowerTimelineId,
          time: (time.millisecondsSinceEpoch / 1000).truncate(),
          mediaType: record.type
      );

      batch.insert(PhotoRecord.tableName, {
        'flowerId': flowerId,
        'flowerTimelineId': flowerTimelineId,
        'time': newRecord.time,
        'file': newPath,
        'thumbnailFile': thumbnailPath,
        'mediaType': record.type.index
      });

      savedRecords.add(newRecord);
    }

    List<dynamic> ids = await batch.commit();
    for (int i = 0; i < ids.length; i++) {
      int id = ids[i];
      savedRecords[i].id = id;
    }

    return savedRecords;
  }


  static Future<List<PhotoRecord>> updateByFlowerTimelineId(
      int flowerId, int flowerTimelineId, List<MediaItem> photos, DateTime time,
      List<PhotoRecord> oldPhotos,
  ) async {
    // 先删除旧的文件记录
    final db = await DB.get();
    await db.sqlite.delete(PhotoRecord.tableName, where: 'flowerTimelineId = ?', whereArgs: [flowerTimelineId]);

    // 插入新的文件记录
    final batch = db.sqlite.batch();
    List<PhotoRecord> savedRecords = [];

    for (final record in photos.reversed) {
      final newPath = await copyToPhotoRecordDir(record.path);

      // 处理缩略图
      String? thumbnailPath;
      if (record.thumbnailPath != null) {
        thumbnailPath = await copyToPhotoRecordDir(record.thumbnailPath!);
      }

      final newRecord = PhotoRecord(
          flowerId: flowerId,
          file: newPath,
          thumbnailFile: thumbnailPath,
          flowerTimelineId: flowerTimelineId,
          time: (time.millisecondsSinceEpoch / 1000).truncate(),
          mediaType: record.type
      );

      batch.insert(PhotoRecord.tableName, {
        'flowerId': flowerId,
        'flowerTimelineId': flowerTimelineId,
        'time': newRecord.time,
        'file': newPath,
        'thumbnailFile': thumbnailPath,
        'mediaType': record.type.index
      });

      savedRecords.add(newRecord);
    }

    List<dynamic> ids = await batch.commit();
    for (int i = 0; i < ids.length; i++) {
      int id = ids[i];
      savedRecords[i].id = id;
    }

    // 删除不在使用的文件
    for (final oldRecord in oldPhotos) {
      final useCount = Sqflite.firstIntValue(
          await db.sqlite.rawQuery('select count(id) from ${PhotoRecord.tableName} where file = ?', [oldRecord.file.split('/').last])) ??
          0;

      if (useCount == 0) {
        final f = File(oldRecord.file);
        if (await f.exists()) {
          await f.delete();
        }
        if (oldRecord.hasThumbnail) {
          final thumbnailFile = File(oldRecord.thumbnailFile);
          if (await thumbnailFile.exists()) {
            await thumbnailFile.delete();
          }
        }
      }
    }

    return savedRecords;
  }

  String get file {
    final dir = Global.photoRecordDir;
    return '$dir/$_file';
  }

  /// 获取缩略图路径，如果没有缩略图，则返回原始文件路径
  String get thumbnailFile {
    final dir = Global.photoRecordDir;
    return '$dir/$_thumbnailFile';
  }

  /// 检查是否有缩略图
  bool get hasThumbnail => _thumbnailFile != null;

  /// 获取用于显示的路径，优先使用缩略图
  String get displayPath => hasThumbnail ? thumbnailFile : file;

  /// 判断是否为视频
  bool get isVideo => mediaType == MediaType.video;

  /// 判断是否为 LivePhoto
  bool get isLivePhoto => mediaType == MediaType.livePhoto;

  /// 判断是否为图片
  bool get isImage => mediaType == MediaType.image;

  static Future<List<PhotoRecord>> get({int? offset, required int limit, required String timeOrder}) async {
    final db = await DB.get();

    List<Map<String, dynamic>> results =
        await db.sqlite.query(tableName, offset: offset, limit: limit, orderBy: 'time $timeOrder');

    return results.map((e) => PhotoRecord.fromDB(e)).toList();
  }

  static Future<List<String>> parsePhotos(String photos) async {
    final dir = Global.photoRecordDir;
    return photos.split(',').map((e) => '$dir/$e').toList();
  }

  static Future<List<PhotoRecord>> getByFilters({
    int? offset,
    required int limit,
    required String timeOrder,
    List<int>? flowerIds,
    List<int>? tagIds,
  }) async {
    final db = await DB.get();

    List<String> conditions = [];
    List<dynamic> whereArgs = [];

    if (flowerIds?.isNotEmpty ?? false) {
      conditions.add('flowerId IN (${flowerIds!.map((_) => '?').join(',')})');
      whereArgs.addAll(flowerIds);
    }

    if (tagIds?.isNotEmpty ?? false) {
      conditions.add(
          'flowerId IN (SELECT flowerId FROM tag_info_mapping WHERE tagId IN (${tagIds!.map((_) => '?').join(',')}))');
      whereArgs.addAll(tagIds);
    }

    List<Map<String, dynamic>> results = await db.sqlite.query(tableName,
        where: conditions.isNotEmpty ? conditions.join(' OR ') : null,
        whereArgs: whereArgs.isNotEmpty ? whereArgs : null,
        offset: offset,
        limit: limit,
        orderBy: 'time $timeOrder');

    return results.map((e) => PhotoRecord.fromDB(e)).toList();
  }
}
