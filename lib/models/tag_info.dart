import 'package:flower_timemachine/common/global.dart';
import 'package:sqflite/sqflite.dart';

class TagInfo {
  TagInfo({required this.id, required this.name, this.sortOrder});

  static const tableName = 'tag_info';

  final int id;
  final String name;
  final int? sortOrder;

  static String createTable() {
    return 'CREATE TABLE $tableName('
        'id INTEGER PRIMARY KEY, '
        'name TEXT, '
        'sort_order INTEGER)';
  }

  static Future<TagInfo> create(String name, {int? sortOrder}) async {
    final db = await DB.get();
    final id = await db.sqlite.insert(tableName, {'name': name, 'sort_order': sortOrder});

    return TagInfo(id: id, name: name, sortOrder: sortOrder);
  }

  static Future<Iterable<TagInfo>> getAllSorted() async {
    final db = await DB.get();
    // 先按照 sort_order 排序（非空值优先），然后按照 id 排序
    List<Map<String, dynamic>> result = await db.sqlite
        .rawQuery('SELECT * FROM $tableName ORDER BY CASE WHEN sort_order IS NULL THEN 1 ELSE 0 END, sort_order, id');

    return result.map((e) => TagInfo(id: e['id'], name: e['name'], sortOrder: e['sort_order']));
  }

  static Future<bool> checkSameTag(String name) async {
    final db = await DB.get();
    final id = Sqflite.firstIntValue(await db.sqlite.rawQuery('SELECT id from $tableName WHERE name = ?', [name]));

    return id != null;
  }

  Future<int> delete() async {
    final db = await DB.get();
    return await db.sqlite.delete(tableName, where: 'id = ?', whereArgs: [id]);
  }

  Future<TagInfo> changeName(String name) async {
    final db = await DB.get();
    await db.sqlite.update(tableName, {"name": name}, where: 'id = ?', whereArgs: [id]);

    return TagInfo(id: id, name: name, sortOrder: sortOrder);
  }

  static Future<void> updateSortOrders(List<TagInfo> tags) async {
    final db = await DB.get();
    final batch = db.sqlite.batch();

    for (var i = 0; i < tags.length; i++) {
      batch.update(tableName, {"sort_order": i}, where: 'id = ?', whereArgs: [tags[i].id]);
    }

    await batch.commit();
  }

  @override
  bool operator ==(Object other) {
    return other is TagInfo && other.id == id && other.name == name && other.sortOrder == sortOrder;
  }

  @override
  int get hashCode => Object.hash(id, name, sortOrder);
}
