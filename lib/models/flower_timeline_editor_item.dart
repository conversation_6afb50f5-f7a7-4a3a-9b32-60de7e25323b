import 'package:flower_timemachine/models/media_item.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'flower_timeline_editor_item.freezed.dart';


@freezed
class FlowerTimelineEditorItem with _$FlowerTimelineEditorItem {
  const factory FlowerTimelineEditorItem({
    required String? text,
    required List<MediaItem> mediaItems,
    required DateTime dateTime
  }) = _FlowerTimelineEditorItem;
}