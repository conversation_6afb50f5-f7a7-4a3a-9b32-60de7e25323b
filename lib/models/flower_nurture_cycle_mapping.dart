

import 'package:flower_timemachine/common/global.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:flower_timemachine/types/flower_nurture_cycle.dart';

class FlowerNurtureCycleMapping {
  static const tableName = 'flower_nurture_cycle';

  final int id;
  final int flowerId;
  final int typeId;
  // 养护时间间隔（单位：天）
  final int cycle;

  FlowerNurtureCycleMapping(this.id, this.flowerId, this.typeId, this.cycle);


  static String createTable() {
    return 'CREATE TABLE $tableName('
        'id INTEGER PRIMARY KEY, '
        'flower int, '
        'type int, '
        'cycle int)';
  }

  static Future<List<FlowerNurtureCycle>> getFlowerCycle(int flowerId) async {
    final db = await DB.get();
    List<Map<String, dynamic>> rows = await db.sqlite.query(tableName, where: "flower = ?", whereArgs: [flowerId]);

    final controller = NurtureTypesController.get();
    List<FlowerNurtureCycle> ret = [];
    for (final row in rows) {
      final t = controller.getTypeInfo(row['type']);
      if (t == null) {  
        continue;
      }

      ret.add(FlowerNurtureCycle(
        t,
        row['cycle']
      ));
    }

    return ret;
  }

  static Future<Map<int, FlowerNurtureCycle>> getFlowerCycleMap(int flowerId) async {
    Map<int, FlowerNurtureCycle> result = {};

    final list = await FlowerNurtureCycleMapping.getFlowerCycle(flowerId);
    for (final item in list) {
      result[item.type.id] = item;
    }

    return result;
  }

  static Future<void> create(int flowerId, List<FlowerNurtureCycle> typeCycle) async {
    final db = await DB.get();
    final batch = db.sqlite.batch();
    for (final item in typeCycle) {
      batch.insert(tableName, {
        "flower": flowerId,
        "type": item.type.id,
        "cycle": item.cycle
      });
    }

    await batch.commit(noResult: true);
  }

  static Future<void> update(int flowerId, List<FlowerNurtureCycle> typeCycle) async {
    final db = await DB.get();
    final batch = db.sqlite.batch();

    batch.delete(tableName, where: 'flower = ?', whereArgs: [flowerId]);
    for (final item in typeCycle) {
      batch.insert(tableName, {
        'flower': flowerId,
        'type': item.type.id,
        'cycle': item.cycle
      });
    }

    await batch.commit(noResult: true);
  }

  static Future<int> deleteByFlower(int flowerId) async {
    final db = await DB.get();
    return await db.sqlite.delete(tableName, where:  'flower = ?', whereArgs: [flowerId]);
  }

  static Future<int> deleteByNurtureType(int nurtureTypeId) async {
    final db = await DB.get();
    return await db.sqlite.delete(tableName, where:  'type = ?', whereArgs: [nurtureTypeId]);
  }
}