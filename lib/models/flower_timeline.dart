import '../common/global.dart';

class FlowerTimeline {
  final int id;

  final int flowerId;

  final String? text;

  // 秒
  final int time;

  static const tableName = "flower_timeline";

  static const idField = "id";
  static const flowerIdField = "flowerId";
  static const textField = "text";
  static const timeField = "time";

  FlowerTimeline({required this.id, required this.flowerId, this.text, required this.time});

  static String createTable() {
    return 'CREATE TABLE $tableName('
        'id INTEGER PRIMARY KEY, '
        'flowerId INTEGER, '
        'text TEXT,'
        'time INTEGER)';
  }

  static Future<List<FlowerTimeline>> get(int flowerId, {int? offset, int? limit}) async {
    final db = await DB.get();

    final List<Map<String, dynamic>> result = await db.sqlite.query(
      tableName,
      where: 'flowerId = ?',
      whereArgs: [flowerId],
      orderBy: 'time DESC',
      offset: offset,
      limit: limit,
    );

    return result.map((e) => fromDB(e)).toList();
  }

  static Future<FlowerTimeline> create(int flowerId, String? text, DateTime time) async {
    final db = await DB.get();
    final t = (time.millisecondsSinceEpoch / 1000).truncate();
    final id = await db.sqlite.insert(tableName, {'flowerId': flowerId, 'text': text, 'time': t});

    return FlowerTimeline(id: id, flowerId: flowerId, time: t, text: text);
  }

  static Future<void> update(int timelineId, String? text, DateTime time) async {
    final db = await DB.get();
    final t = (time.millisecondsSinceEpoch / 1000).truncate();
    await db.sqlite.update(tableName, {'text': text, 'time': t}, where: "id = ?", whereArgs: [timelineId]);
  }

  static FlowerTimeline fromDB(Map<String, dynamic> row) =>
      FlowerTimeline(id: row['id'], flowerId: row['flowerId'], text: row['text'], time: row['time']);

  static Future<int> deleteById(int id) async {
    final db = await DB.get();
    return await db.sqlite.delete(tableName, where: "id = ?", whereArgs: [id]);
  }

  static Future<int> deleteByFlowerId(int flowerId) async {
    final db = await DB.get();
    return await db.sqlite.delete(tableName, where: "flowerId = ?", whereArgs: [flowerId]);
  }
}
